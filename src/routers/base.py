#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File    :   base_url.py
@Time    :   2024/05/11 14:36:06
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON><PERSON>
@Desc    :   None
"""
from typing import Union

from fastapi import Depends
from fastapi import HTTPException
from fastapi import status
from fastapi.exceptions import RequestValidationError
from fastapi.security import HTTPAuthorizationCredentials
from fastapi.security import <PERSON>TT<PERSON><PERSON>earer
from pydantic import BaseModel
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette.responses import Response

from src.excption_handler.token_error import TokenError
from src.services.check_token import CheckTokenService

security = HTTPBearer()


def authenticate_user(token: HTTPAuthorizationCredentials = Depends(security)):
    """
    验证 token

    @Parameters  :

    @Returns :

    """
    if CheckTokenService.is_valid_token(token=token.credentials):
        return token.credentials
    else:
        return HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"Authenticate": "Bearer"},
        )


class RespModel(BaseModel):
    code: int
    msg: str
    data: Union[list, dict, str]


async def http_exception_handler(request: Request, exc: HTTPException):
    return resp_error(str(exc.detail), status_code=exc.status_code)


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    errors = list(exc.errors())
    if len(errors) > 0:
        return resp_error(str(errors[0]["msg"]), data=errors)
    return resp_error(str(exc), data=errors)


async def token_error_handler(request, exc: TokenError):
    return JSONResponse(
        content={"error": "Token error", "detail": exc.detail},
        status_code=exc.status_code,
    )


def resp_ok(data: Union[list, dict, str], status_code=status.HTTP_200_OK) -> Response:
    return JSONResponse(
        status_code=status_code,
        content={
            "code": 0,
            "msg": "success",
            "data": data,
        },
    )


def resp_error(
    message: str, code: int = 1, data: Union[list, dict, str] = None, status_code=status.HTTP_400_BAD_REQUEST
) -> Response:
    return JSONResponse(
        status_code=status_code,
        content={
            "code": code,
            "msg": message,
            "data": data,
        },
    )
