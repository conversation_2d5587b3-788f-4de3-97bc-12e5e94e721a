import json
from functools import wraps

from conf import settings


def mocked_json(file_path=None, result=None):

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            res = result
            if not settings.DEBUG:
                return func(*args, **kwargs)
            if file_path:
                with open(file_path, "r") as f:
                    res = json.loads(f.read())
            return res

        return wrapper

    return decorator
