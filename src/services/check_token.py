#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File    :   check_token_service.py
@Time    :   2023/08/15 17:23:02
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Desc    :   校验 token服务
"""

from functools import lru_cache

import requests
from fastapi import HTTPException

from conf.settings import AUTH_HOST
from ..excption_handler.token_error import TokenError


class CheckTokenService:
    """

    校验 token 服务

    """

    host = AUTH_HOST

    @classmethod
    @lru_cache(maxsize=100)
    def get_token_info(cls, token: str):
        return requests.get(AUTH_HOST, params={"token": token})

    @classmethod
    @lru_cache(maxsize=100)
    def is_valid_token(cls, token: str) -> bool:
        """
        判断 token是否有效：
        规则 1： token 存在且在有限期内
        后续可以在此添加更新 token 校验规则
        @Parameters  :

        @Returns :

        """
        return token
        try:
            token_info = cls.get_token_info(token=token)
        except HTTPException as e:
            raise HTTPException(status_code=e.status_code, detail=e.detail)

        if token_info is None:
            # token 不存在
            raise TokenError(detail="Token is not exists")

        if not token_info.is_within_deadline():
            # token 过期了
            raise TokenError(detail="Token is out of deadline")

        return True
