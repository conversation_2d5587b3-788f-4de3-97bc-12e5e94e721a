import logging

from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

from spider.spiders.nbs import Nbs<PERSON>pider
from src.services.task_impl.base import start_spider

logger = logging.getLogger("crawl")


scrapy_settings = get_project_settings()

# 国家统计局国务院信息
# 首页为 https://www.stats.gov.cn/xw/szyw/gwyxx/index.html ，翻页分别为 index_1.html, index_2.html, ...以此类推


def get_page_url(page: int) -> str:
    if int(page) == 1:
        return "https://www.stats.gov.cn/xw/szyw/gwyxx/index.html"
    return f"https://www.stats.gov.cn/xw/szyw/gwyxx/index_{page}.html"


def nbs_crawl_task(target, task_id):
    process = CrawlerProcess(scrapy_settings)

    page = 1
    if target.get("page"):
        page = target["page"]
    page_url = get_page_url(page)

    # 启动爬虫
    process.crawl(
        NbsSpider,
        start_urls=[page_url],
        target_id=target["target_id"],
        task_id=task_id,
    )

    return start_spider(process, task_id, [])
