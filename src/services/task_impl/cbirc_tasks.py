import logging

from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from tenacity import retry, retry_if_exception_type
from tenacity import retry_if_not_result
from tenacity import stop_after_attempt
from tenacity import wait_fixed

from spider.spiders.cbirc import CbircSpider
from src.dao.open_gauss.spider_results import ResultDao
from src.services.task_impl.base import start_spider
from src.storage.open_gauss.connection import get_session
from src.utils.request import get_random_ua, get_retry_request_session

logger = logging.getLogger("crawl")


scrapy_settings = get_project_settings()

# 国家金融监督管理总局 政策解读 https://www.nfra.gov.cn/cn/view/pages/ItemList.html?itemPId=923&itemId=928&itemUrl=ItemListRightList.html&itemName=%E6%94%BF%E7%AD%96%E8%A7%84%E7%AB%A0%E8%A7%84%E8%8C%83%E6%80%A7%E6%96%87%E4%BB%B6&itemsubPId=926
# 页面是动态渲染的，通过 https://www.nfra.gov.cn/cbircweb/DocInfo/SelectDocByItemIdAndChild?itemId=928&pageSize=18&pageIndex=1 获取列表
# 通过 https://www.nfra.gov.cn/cn/static/data/DocInfo/SelectByDocId/data_docId=1165004.json 获取文章详情


def code_ok(value):
    return str(value.get("rptCode") if value else None) == "200"


@retry(
    retry=(retry_if_not_result(code_ok) | retry_if_exception_type()),
    wait=wait_fixed(5),
    stop=stop_after_attempt(3),
)
def get_list(page):
    session = get_retry_request_session()
    post_history = session.get(
        f"https://www.nfra.gov.cn/cbircweb/DocInfo/SelectDocByItemIdAndChild?"
        f"itemId=928&pageSize=18&pageIndex={page}",
        headers={
            "User-Agent": get_random_ua(),
            "Referer": "https://www.nfra.gov.cn/cn/view/pages/ItemList.html?itemPId=923&itemId=928&itemUrl=ItemListRightList.html&itemName=%E6%94%BF%E7%AD%96%E8%A7%84%E7%AB%A0%E8%A7%84%E8%8C%83%E6%80%A7%E6%96%87%E4%BB%B6&itemsubPId=926",
        },
    )
    return post_history.json()


def cbirc_crawl_task(target, task_id):
    page = 1
    if target.get("page"):
        page = target["page"]
    resp = get_list(page)
    articles = resp["data"]["rows"]
    article_aids = [str(article["docId"]) for article in articles]
    with get_session() as db:
        non_exist_aids = ResultDao.filter_exist_aids(db, target["target_id"], article_aids)

    articles_to_crawl = [article for article in articles if str(article["docId"]) in non_exist_aids]

    process = CrawlerProcess(scrapy_settings)

    # 启动爬虫
    process.crawl(
        CbircSpider,
        infos=articles_to_crawl,
        target_id=target["target_id"],
        task_id=task_id,
    )

    return start_spider(process, task_id, list(article_aids))
