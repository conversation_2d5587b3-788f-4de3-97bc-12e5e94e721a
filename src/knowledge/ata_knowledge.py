from django.conf import settings

from .ata_100 import ATA_100
from .embedding import EmbeddingServer
from .faiss import FaissVector
from .markdow_spitter import MarkdownSpitter

VECTOR = None


class ATAKnowledgeService:
    def get_knowledge_index_name(self):
        return f"ata_knowledge-{settings.EMBEDDING_MODEL.split('/')[-1]}"

    def init_ata_knowledge(self):
        # 先按照英文方式分割文档，适合大多数情况
        embedding = EmbeddingServer(
            settings.EMBEDDING_MODEL,
            f"{settings.EMBEDDING_API_URL}/embeddings",
            api_key=settings.EMBEDDING_API_KEY,
            batch_size=settings.EMBEDDING_BATCH_SIZE,
        )
        split_docs = MarkdownSpitter(settings.KNOWLEDGE_DIR.resolve().as_posix(), language="en").spliters_docs(
            [("###", "header 3")]
        )
        vector = FaissVector.texts_embedding(split_docs, embedding)

        vector.save_vector(settings.KNOWLEDGE_DIR.resolve().as_posix(), self.get_knowledge_index_name())
        return vector

    def load_vector(self):
        global VECTOR
        if VECTOR:
            return VECTOR

        embedding = EmbeddingServer(
            settings.EMBEDDING_MODEL, f"{settings.EMBEDDING_API_URL}/embeddings", api_key=settings.EMBEDDING_API_KEY
        )

        file_name = self.get_knowledge_index_name()
        file_path = settings.KNOWLEDGE_DIR / f"{file_name}.bin"
        if not file_path.exists():
            self.init_ata_knowledge()

        VECTOR = FaissVector.load_vector(embedding, file_path.parent.resolve().as_posix(), file_name)
        return VECTOR

    def search(self, text: str, top_k: int = 25):
        vector = self.load_vector()
        query_vector = vector.query_embedding(text)
        return vector.search_by_query_vector_with_score(query_vector, top_k)

    def get_code_description(self, code: int | str | None = None, is_label: bool = False):
        if code:
            return {str(code): ATA_100.get(str(code), "")}
        if is_label:
            return {
                "two_digit_codes": [
                    {"label": f"{code} {des}", "value": code} for code, des in ATA_100.items() if len(code) == 2
                ],
                "four_digit_codes": [
                    {"label": f"{code} {des}", "value": code} for code, des in ATA_100.items() if len(code) == 4
                ],
            }
        return ATA_100
