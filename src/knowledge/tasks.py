from __future__ import absolute_import, unicode_literals

import io
import logging
import re
from typing import List, Tuple

import docx
import numpy as np
import orjson
import pymupdf
import spacy
from asgiref.sync import async_to_sync
from celery import shared_task
from django.conf import settings
from django_redis import get_redis_connection
from requests import HTTPError

from django.conf import settings
from codes import CODE_LLM_SERVER_ERROR
from exceptions import APIError
from file.models import File, FileCategory
from knowledge.consts import (
    ACCIDENT_CATEGORIES_SET,
    CHUNK_CATEGORIES_SET,
    get_accident_update_key,
    get_chunk_update_key,
)
from knowledge.models import AccidentReport, Chunk
from utils import extract_codes, normalize_date, parse_json_markdown

logger = logging.getLogger(__name__)

# Load spaCy models
try:
    nlp_zh = spacy.load("zh_core_web_sm")
    nlp_en = spacy.load("en_core_web_sm")
except IOError:
    logger.error(
        "spaCy models not found. Please download them using: python -m spacy download zh_core_web_sm / en_core_web_sm"
    )
    raise


def split_pdf_text(text):
    """
    Split PDF text based on multiple empty or whitespace-only lines.
    This function considers lines with only whitespace as potential separators.
    """
    lines = text.split("\n")
    paragraphs = []
    current_paragraph = []
    empty_line_count = 0

    for line in lines:
        if line.strip() == "":
            empty_line_count += 1
        else:
            if empty_line_count >= 2:
                if current_paragraph:
                    paragraphs.append("\n".join(current_paragraph))
                    current_paragraph = []
            current_paragraph.append(line)
            empty_line_count = 0

    if current_paragraph:
        paragraphs.append("\n".join(current_paragraph))

    return paragraphs


def split_paragraph(para):
    """
    Split a paragraph into sentences while preserving punctuation.
    """
    # This regex looks for sentence-ending punctuation followed by spaces and a capital letter
    sentences = re.split(r"(?<=[。！？.!?])\s*(?=[A-Z\u4e00-\u9fa5])", para)
    return [s.strip() for s in sentences if s.strip()]


def process_long_sentence(sentence, max_length, language):
    """
    Process a long sentence by splitting it into smaller chunks while preserving punctuation.
    This function now handles the NLP model selection internally.
    """
    nlp = nlp_zh if language.lower() in ["zh", "ch", "chinese"] else nlp_en
    doc = nlp(sentence)

    chunks = []
    current_chunk = []
    current_length = 0

    for token in doc:
        token_text = token.text_with_ws  # This preserves whitespace
        token_length = len(token_text)

        if current_length + token_length > max_length:
            if current_chunk:
                chunks.append("".join(current_chunk))
                current_chunk = []
                current_length = 0

        current_chunk.append(token_text)
        current_length += token_length

        # If this token ends with sentence-ending punctuation, consider ending the chunk
        if token.is_sent_end and current_length >= settings.MIN_CHUNK_LENGTH:
            chunks.append("".join(current_chunk))
            current_chunk = []
            current_length = 0

    if current_chunk:
        chunks.append("".join(current_chunk))

    return chunks


def split_text_into_chunks(
    text, page_number=None, min_length=settings.MIN_CHUNK_LENGTH, max_length=settings.MAX_CHUNK_LENGTH,
    language="zh", is_pdf=False
):
    chunks = []
    current_chunk = []
    current_length = 0

    if is_pdf:
        paragraphs = split_pdf_text(text)
        sentences = []
        for para in paragraphs:
            sentences.extend(split_paragraph(para))
    else:
        sentences = split_paragraph(text)

    for sentence in sentences:
        sentence_length = len(sentence)

        if current_length + sentence_length <= max_length:
            current_chunk.append(sentence)
            current_length += sentence_length
        else:
            if current_chunk and current_length >= min_length:
                chunks.append(("".join(current_chunk), page_number))
                current_chunk = []
                current_length = 0

            if sentence_length > max_length:
                sub_chunks = process_long_sentence(sentence, max_length, language=language)
                for sub_chunk in sub_chunks:
                    if len(sub_chunk) > max_length:
                        chunks.append((sub_chunk[:max_length], page_number))
                    else:
                        if current_length + len(sub_chunk) > max_length:
                            if current_chunk:
                                chunks.append(("".join(current_chunk), page_number))
                                current_chunk = []
                                current_length = 0
                        current_chunk.append(sub_chunk)
                        current_length += len(sub_chunk)
            else:
                current_chunk.append(sentence)
                current_length += sentence_length

    if current_chunk:
        chunks.append(("".join(current_chunk), page_number))

    # Attempt to merge the last short chunk if possible
    if len(chunks) > 1:
        last_chunk, last_page = chunks[-1]
        second_last_chunk, second_last_page = chunks[-2]

        if last_page == second_last_page and len(last_chunk) < min_length:
            combined_length = len(second_last_chunk) + len(last_chunk)
            if combined_length <= max_length:
                merged_chunk = second_last_chunk + last_chunk
                chunks[-2] = (merged_chunk, second_last_page)
                chunks.pop()

    return chunks


def extract_text_from_pdf(file_content) -> List[Tuple[str, int]]:
    with io.BytesIO(file_content) as pdf_stream:
        doc = pymupdf.open(stream=pdf_stream, filetype="pdf")
        pages = []
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            pages.append((text, page_num + 1))
    return pages


def extract_text_from_doc(file_content) -> List[Tuple[str, int]]:
    with io.BytesIO(file_content) as doc_stream:
        doc = docx.Document(doc_stream)
        pages = []
        current_page = 1
        current_text = ""
        for para in doc.paragraphs:
            current_text += para.text + "\n"
            if para.runs and para.runs[-1].element.xpath("./w:br[@w:type='page']"):
                pages.append((current_text, current_page))
                current_page += 1
                current_text = ""
        if current_text:
            pages.append((current_text, current_page))
    return pages


def extract_text_from_text(file_content) -> List[Tuple[str, None]]:
    text = file_content.decode("utf-8")
    return [(text, None)]


def get_pages_from_file(file):
    # Read file content from object storage
    with file.file.open("rb") as file_obj:
        file_content = file_obj.read()
    # Extract text based on file type
    if file.file_type == File.FILE_TYPE_PDF:
        pages = extract_text_from_pdf(file_content)
    elif file.file_type == File.FILE_TYPE_DOC:
        pages = extract_text_from_doc(file_content)
    elif file.file_type == File.FILE_TYPE_TEXT:
        pages = extract_text_from_text(file_content)
    else:
        raise ValueError(f"Unsupported file type: {file.file_type}")
    return pages


def chunk_file(file: File, pages, user_id=None):
    all_chunk_objs = []

    # Delete existing chunks for this file
    Chunk.objects.filter(file_id=file.id).delete()

    # Split text into chunks
    for page_text, page_number in pages:
        chunks = split_text_into_chunks(
            page_text, page_number, language=file.language, is_pdf=(file.file_type == File.FILE_TYPE_PDF)
        )
        for i, (chunk_text, chunk_page_number) in enumerate(chunks):
            chunk_obj = Chunk.objects.create(
                category_id=file.category_id,
                file_id=file.id,
                content=chunk_text,
                page_number=chunk_page_number,
                position=i,
                created_by=user_id,
            )
            all_chunk_objs.append(chunk_obj)

    return all_chunk_objs


async def analysis_accident_text(text):
    from knowledge.services import KnowledgeService

    knowledge_service = KnowledgeService()
    result = await knowledge_service.analysis_description(text)

    try:
        result_json = parse_json_markdown(result)
    except Exception as ex:
        logger.error(f"Error decoding JSON: {str(ex)}")
        result_json = {}
    return result_json


async def analysis_ata_code_from_text(text):
    from knowledge.services import KnowledgeService

    knowledge_service = KnowledgeService()
    summary, result = await knowledge_service.analysis_ata_code(text, "en")
    try:
        result_json = parse_json_markdown(result)
    except Exception as ex:
        logger.error(f"Error decoding JSON: {str(ex)} Response: {result}")
        result_json = {}

    return summary, result_json


async def analysis_accident_text_and_save_report(
    file_id=None, text="", pages=None, user_id=None, save=True, report_type=None, raise_exception=False
):
    report = None
    if file_id:
        report = await AccidentReport.objects.filter(file_id=file_id).afirst()
    if not report:
        report = AccidentReport(
            file_id=file_id,
            update_status=AccidentReport.STATUS_PROCESSING,
            created_by=user_id,
            report_type=report_type,
        )

    try:
        all_text = text
        if pages:
            for page_text, _ in pages:
                all_text += page_text

        if not all_text:
            # No text to analyze
            return None

        report.incident_description = all_text

        accident_info = await analysis_accident_text(all_text)

        summary, analysis_result = await analysis_ata_code_from_text(all_text)

        report.accident_date = normalize_date(accident_info.get("事故时间", "").strip())
        report.aircraft_type = accident_info.get("飞机型号", "").strip()
        report.flight_phase = accident_info.get("飞行阶段", "").strip()
        report.event_level = accident_info.get("事故等级", "").strip()
        report.event_type = accident_info.get("事故分类", "").strip()
        report.event_cause = accident_info.get("事故原因", "").strip()
        report.incident_id = accident_info.get("事故编号", "").strip()

        report.cause_analysis = summary
        report.cause_analysis_result = orjson.dumps(analysis_result).decode("utf-8")
        report.two_digit_code, report.four_digit_code = extract_codes(
            (analysis_result.get("Machine", "null") or "null").strip()
        )

        report.update_status = AccidentReport.STATUS_COMPLETED
    except Exception as e:
        report.update_status = AccidentReport.STATUS_FAILED
        logger.error(f"Error analyzing accident text for file {file_id}: {str(e)}", exc_info=True)
        if raise_exception:
            if isinstance(e, HTTPError):
                raise APIError(detail="大模型服务器报错：" + e.response.text, code=CODE_LLM_SERVER_ERROR) from e
            raise e
    finally:
        if save:
            await report.asave()
    return report


@shared_task
def process_file_task(file_id: str, user_id=None):
    file = File.objects.get(id=file_id)
    chunks = process_file(file, user_id=user_id)
    return {"chunk_ids": [chunk.id for chunk in chunks]}


def process_file(file: File, user_id=None, save_report=False):
    file.processing_status = File.STATUS_PROCESSING
    file.save()

    try:
        pages = get_pages_from_file(file)

        if save_report:
            async_to_sync(analysis_accident_text_and_save_report)(file_id=file.id, pages=pages, user_id=user_id)

        chunks = chunk_file(file, pages, user_id=user_id)

        file.processing_status = File.STATUS_CHUNKED
        return chunks
    except Exception as e:
        file.processing_status = File.STATUS_FAILED
        logger.error(f"Error processing file {file.id}: {str(e)}", exc_info=True)
    finally:
        file.save()


@shared_task
def process_documents(category_id, user_id=None):
    category = FileCategory.objects.get(id=category_id)
    documents = File.objects.filter(
        category=category, file_type__in=[File.FILE_TYPE_PDF, File.FILE_TYPE_DOC, File.FILE_TYPE_TEXT]
    )

    all_chunk_objs = []

    Chunk.objects.filter(category_id=category_id).exclude(file_id__in=documents.values_list("id", flat=True)).delete()

    for doc_file in documents:
        chunks = process_file(doc_file, user_id=user_id)
        all_chunk_objs.extend(chunks)

    return {"chunk_ids": [chunk.id for chunk in all_chunk_objs]}


@shared_task
def vectorize_and_index_chunks(category_id):
    category = FileCategory.objects.get(id=category_id)
    chunks = Chunk.objects.filter(category_id=category_id)

    # Initialize FAISS index
    import faiss

    index = None

    from knowledge.services import KnowledgeService

    try:
        # Process chunks in batches
        for i in range(0, len(chunks), EMBEDDING_BATCH_SIZE):
            batch = chunks[i : i + EMBEDDING_BATCH_SIZE]

            # Generate embeddings
            embeddings = KnowledgeService.get_embeddings([chunk.content for chunk in batch])

            # Initialize FAISS index if not done yet
            if index is None:
                embedding_dimension = len(embeddings[0])
                index = faiss.IndexFlatL2(embedding_dimension)

            # Add to FAISS index
            index.add(np.array(embeddings, dtype=np.float32))

            # Update chunks with vector information
            for j, (chunk, _embedding) in enumerate(zip(batch, embeddings)):
                chunk.vector_id = index.ntotal - len(batch) + j
                chunk.save(update_fields=["vector_id"])

        # Save FAISS index
        if index is not None:
            index_path = KnowledgeService.get_index_path(category_id)
            faiss.write_index(index, index_path)

        return f"Vectorized and indexed chunks for category: {category.name}"
    except Exception as e:
        logger.error(f"Error vectorizing and indexing chunks for category {category_id}: {str(e)}", exc_info=True)
        return f"Failed to vectorize and index chunks for category: {category.name}"


@shared_task(name="check_and_run_tasks", ignore_result=True)
def check_and_run_tasks():
    import time

    current_time = time.time()
    redis_conn = get_redis_connection("default")

    # 处理 Chunk 类别
    chunk_categories = redis_conn.smembers(CHUNK_CATEGORIES_SET)
    for category_id_bytes in chunk_categories:
        category_id = int(category_id_bytes.decode())
        chunk_update_key = get_chunk_update_key(category_id)
        last_update = redis_conn.get(chunk_update_key)
        if last_update and current_time - float(last_update) >= settings.DEBOUNCE_TIMEOUT:
            vectorize_and_index_chunks.delay(category_id)
            # 移除已处理的类别
            redis_conn.delete(chunk_update_key)
            redis_conn.srem(CHUNK_CATEGORIES_SET, category_id)

    # 处理 AccidentReport 类别
    accident_categories = redis_conn.smembers(ACCIDENT_CATEGORIES_SET)
    from graph.tasks import import_data_to_neo4j

    for category_id_bytes in accident_categories:
        category_id = int(category_id_bytes.decode())
        accident_update_key = get_accident_update_key(category_id)
        last_update = redis_conn.get(accident_update_key)
        if last_update and current_time - float(last_update) >= settings.DEBOUNCE_TIMEOUT:
            import_data_to_neo4j.delay()
            # 移除已处理的类别
            redis_conn.delete(accident_update_key)
            redis_conn.srem(ACCIDENT_CATEGORIES_SET, category_id)
