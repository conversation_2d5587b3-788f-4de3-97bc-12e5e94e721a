#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    :   faiss.py
@Time    :   2024/05/26 14:08:44
<AUTHOR>   Mason
@Version :   1.0
@Desc    :   FAISS向量化存储与检索
"""

import pickle
import uuid
from pathlib import Path

import numpy as np


class FaissVector:
    def __init__(self, doc_embeds, embedding_func, vector_index, index_to_texts_id: dict[int, str]) -> None:
        self.doc_embeds = doc_embeds
        self.embedding_func = embedding_func
        self.vector_index = vector_index
        self.index_to_texts_id = index_to_texts_id

    def __create_index(self, texts, embeddings, ids=None):
        import faiss

        vector = np.array(embeddings, dtype=np.float32)
        self.vector_index = faiss.IndexFlatL2(vector.shape[1])
        self.vector_index.add(vector)

        ids = ids or [str(uuid.uuid4()) for _ in texts]
        self.doc_embeds = {_id: text for _id, text in zip(ids, texts, strict=False)}
        index_to_ids = {i: uuid for i, uuid in enumerate(ids)}
        self.index_to_texts_id.update(index_to_ids)

        return ids

    def docs_embedding(self, docs: list) -> list:
        return self.embedding_func.embed_text(docs)

    def query_embedding(self, text: str) -> list:
        return self.embedding_func.embed_query(text)

    def save_vector(self, output_path: str, vector_name: str = "index"):
        import faiss

        path = Path(output_path)
        if not path.exists():
            path.mkdir(parents=True)
        faiss.write_index(self.vector_index, str(path / f"{vector_name}.bin"))
        # save docstore and index_to_docstore_id
        with open(path / f"{vector_name}.pkl", "wb") as f:
            pickle.dump((self.doc_embeds, self.index_to_texts_id), f)

    @classmethod
    def load_vector(cls, embedding, input_path: str, vector_name: str = "index", **kwargs):
        import faiss

        path = Path(input_path)
        vector_index = faiss.read_index(str(path / f"{vector_name}.bin"))
        # load docstore and index_to_docstore_id
        with open(path / f"{vector_name}.pkl", "rb") as f:
            doc_embeds, index_to_texts_id = pickle.load(f)
        return cls(doc_embeds, embedding, vector_index, index_to_texts_id, **kwargs)

    @classmethod
    def texts_embedding(cls, texts, embedding, ids=None, **kwargs):
        import faiss

        """文本向量化"""
        embeddings = embedding.embed_text(texts)
        # index = faiss.index_factory(len(embeddings[0]), 'Flat', faiss.METRIC_L2)
        index = faiss.IndexFlatL2(len(embeddings[0]))
        doc_embeds = kwargs.pop("doc_embeds", "")
        index_to_texts_id = kwargs.pop("index_to_texts_id", {})
        vector = cls(doc_embeds, embedding, index, index_to_texts_id, **kwargs)
        vector.__create_index(texts, embeddings, ids)
        return vector

    def search_by_query_vector_with_score(self, query_vector: list[float], top_k: int = 15):
        query_vector = np.array([query_vector], dtype=np.float32)
        scores, indexs = self.vector_index.search(query_vector, top_k)
        texts = []
        for i, j in enumerate(indexs[0]):
            _id = self.index_to_texts_id[j]
            text = self.doc_embeds[_id]
            texts.append((text, scores[0][i]))
        return texts
