from django.core.management.base import BaseCommand
from django.db import models, transaction
from django.utils import timezone
from orjson import orjson

from knowledge.models import AccidentReport
from utils import extract_codes, normalize_date


# 定义临时模型来映射数据库表
class CAACIncidentAnalysisEN(models.Model):
    accident_no = models.CharField(max_length=255, null=True, primary_key=True)
    o_event_date = models.TextField(null=True)
    o_incident_description = models.TextField(null=True)
    o_aircraft_model = models.TextField(null=True)
    o_incident_phase = models.TextField(null=True)
    o_event_level = models.TextField(null=True)
    o_event_type = models.TextField(null=True)
    o_event_cause = models.TextField(null=True)
    o_cause_analysis = models.TextField(null=True)
    o_cause_code = models.TextField(null=True)
    o_cause_analysis_result = models.TextField(null=True)
    update_status = models.IntegerField(null=True)

    class Meta:
        managed = False
        db_table = "caac_aircraft_incidents_analysis_en"
        app_label = "data_import"


class CAACIncidentAnalysis(models.Model):
    incident_id = models.IntegerField(primary_key=True)
    o_event_date = models.TextField(null=True)
    o_incident_description = models.TextField(null=True)
    o_aircraft_model = models.TextField(null=True)
    o_incident_phase = models.TextField(null=True)
    o_event_level = models.TextField(null=True)
    o_event_type = models.TextField(null=True)
    o_event_cause = models.TextField(null=True)
    o_cause_analysis = models.TextField(null=True)
    o_cause_code = models.TextField(null=True)
    o_cause_analysis_result = models.TextField(null=True)
    update_status = models.IntegerField(null=True)

    class Meta:
        managed = False
        db_table = "caac_aircraft_incidents_analysis"
        app_label = "data_import"


class Command(BaseCommand):
    help = "从已有的 caac_aircraft_incidents_analysis 和 caac_aircraft_incidents_analysis_en 数据表导入数据到 AccidentReport 模型中。"

    def add_arguments(self, parser):
        parser.add_argument("source", type=str, choices=["CN", "EN"], help="数据源 (CN 或 EN)")
        parser.add_argument("-l", "--limit", type=int, default=0, help="要导入的数量 (0 表示导入所有数据)")

    def safe_get(self, obj, attr, default=""):
        """安全地获取属性值，如果为None则返回默认值"""
        value = getattr(obj, attr)
        return value if value is not None else default

    def try_get_machine(self, string):
        try:
            json_obj = orjson.loads(string)
            return json_obj.get("Machine", "")
        except Exception:
            return ""

    def import_or_update_record(self, incident, source):
        incident_id = self.safe_get(incident, "accident_no" if source == "EN" else "incident_id")
        incident_id = str(incident_id)  # 确保incident_id是字符串
        if source == "CN":
            cause_code = self.safe_get(incident, "o_cause_code").strip()
        else:
            cause_code = self.try_get_machine(self.safe_get(incident, "o_cause_code"))
        two_digit_code, four_digit_code = extract_codes(cause_code)

        defaults = {
            "accident_date": normalize_date(self.safe_get(incident, "o_event_date")),
            "incident_description": self.safe_get(incident, "o_incident_description").strip(),
            "aircraft_type": self.safe_get(incident, "o_aircraft_model").strip(),
            "flight_phase": self.safe_get(incident, "o_incident_phase").strip(),
            "event_level": self.safe_get(incident, "o_event_level").strip(),
            "event_type": self.safe_get(incident, "o_event_type").strip(),
            "event_cause": self.safe_get(incident, "o_event_cause").strip(),
            "cause_analysis": self.safe_get(incident, "o_cause_analysis").strip()
            if source == "CN"
            else self.safe_get(incident, "o_cause_analysis_result").strip(),
            "two_digit_code": two_digit_code,
            "four_digit_code": four_digit_code,
            "cause_analysis_result": self.safe_get(incident, "o_cause_analysis_result").strip()
            if source == "CN"
            else self.safe_get(incident, "o_cause_code").strip(),
            "update_status": AccidentReport.STATUS_COMPLETED,
            "category_id": 0,
            "update_time": timezone.now(),
        }

        obj, created = AccidentReport.objects.update_or_create(
            incident_id=incident_id,
            category_id=0,
            report_type="cn" if source.lower() == "cn" else "en",
            defaults=defaults,
        )

        return created

    @transaction.atomic
    def handle(self, *args, **options):
        source = options["source"]
        limit = options["limit"]

        new_records = 0
        updated_records = 0

        if source == "EN":
            queryset = CAACIncidentAnalysisEN.objects.order_by("pk")
        else:
            queryset = CAACIncidentAnalysis.objects.order_by("pk")

        if limit > 0:
            queryset = queryset[:limit]

        for incident in queryset:
            if self.import_or_update_record(incident, source):
                new_records += 1
            else:
                updated_records += 1

        self.stdout.write(self.style.SUCCESS(f"数据导入成功完成。新增记录：{new_records}，更新记录：{updated_records}"))
