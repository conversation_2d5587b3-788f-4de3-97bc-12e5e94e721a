from typing import Any, Dict, Tuple, Type, TypeVar

from neomodel import StructuredNode, db

T = TypeVar("T", bound="CustomStructuredNode")


class CustomStructuredNode(StructuredNode):
    """
    扩展 StructuredNode 以支持多属性唯一约束的 get_or_create
    """

    __abstract_node__ = True  # 标记为抽象基类

    @classmethod
    def get_or_create(cls: Type[T], *props: Dict[str, Any], **kwargs: Any) -> Tuple[T, bool]:
        """
        获取或创建节点，支持多属性唯一约束。
        如果节点存在，返回 (node, False)；
        如果节点不存在并创建新节点，返回 (node, True)。

        用法示例：
        >>> node, created = MyNode.get_or_create(
        ...     {'name': 'test', 'category_id': 1},  # 唯一约束属性
        ...     other_prop='value'  # 其他属性
        ... )
        """
        # 确保提供了唯一约束属性
        if not props or not isinstance(props[0], dict):
            raise ValueError("First argument must be a dictionary of unique constraint properties")

        unique_props = props[0]

        # 构建查询条件
        conditions = []
        params = {}
        for key, value in unique_props.items():
            param_name = f"unique_{key}"
            conditions.append(f"n.{key} = ${param_name}")
            params[param_name] = value

        # 构建 Cypher 查询
        query = f"""
        MATCH (n:{cls.__label__})
        WHERE {" AND ".join(conditions)}
        RETURN n
        """

        # 执行查询
        results, _ = db.cypher_query(query, params)

        # 如果找到节点，返回已存在的节点
        if results:
            node = cls.inflate(results[0][0])
            return node, False

        # 如果没找到节点，创建新节点
        props = list(props)
        create_props = props[0].copy()  # 复制唯一约束属性
        create_props.update(kwargs)  # 添加其他属性
        props[0] = create_props

        created = super().create(*props, **kwargs)
        return created[0], True

    @classmethod
    def get_or_none(cls: Type[T], *props: Dict[str, Any]) -> T | None:
        """
        根据多个属性查找节点，如果不存在返回 None。

        用法示例：
        >>> node = MyNode.get_or_none({'name': 'test', 'category_id': 1})
        """
        if not props or not isinstance(props[0], dict):
            raise ValueError("First argument must be a dictionary of properties")

        conditions = []
        params = {}
        for key, value in props[0].items():
            param_name = f"param_{key}"
            conditions.append(f"n.{key} = ${param_name}")
            params[param_name] = value

        query = f"""
        MATCH (n:{cls.__label__})
        WHERE {" AND ".join(conditions)}
        RETURN n
        LIMIT 1
        """

        results, _ = db.cypher_query(query, params)
        if results:
            return cls.inflate(results[0][0])
        return None
