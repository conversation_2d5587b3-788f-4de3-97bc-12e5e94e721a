import logging
from typing import Any

from django.core.management.base import BaseCommand, CommandParser
from neomodel import install_all_labels

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Initialize Neo4j database with labels and constraints，初始化 Neo4j 数据库"

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force recreation of constraints even if they exist",
        )

    def _install_labels(self) -> None:
        """安装 neomodel 标签"""
        try:
            install_all_labels()
            self.stdout.write("Successfully installed all Neo4j labels")
        except Exception as e:
            self.stderr.write(f"Error installing Neo4j labels: {str(e)}")
            raise

    def handle(self, *args: Any, **options: Any) -> None:
        self.stdout.write("Starting Neo4j initialization...")

        try:
            # 先安装标签
            self.stdout.write("Installing Neo4j labels...")
            self._install_labels()

            # 创建复合约束
            self.stdout.write("Creating composite constraints...")
            from graph.algorithms import GraphAlgoService

            algo_service = GraphAlgoService()
            # algo_service.create_composite_constraints(force=options["force"], stdout=self.stdout)

            # 创建全文索引
            algo_service.create_fulltext_index()

            # 显示统计信息
            algo_service.get_graph_statistics(stdout=self.stdout)

            self.stdout.write(self.style.SUCCESS("Successfully initialized Neo4j database"))

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error during Neo4j initialization: {str(e)}"))
            raise
