from enum import Enum
from typing import Any, <PERSON><PERSON>, TypeVar

from django.db.models import Model
from ninja import Schema
from ninja_extra.exceptions import NotFound
from ninja_extra.pagination import PageNumberPaginationExtra

from codes import CODE_SUCCESS

T = TypeVar("T")


class APIResponseSchema(Schema, Generic[T]):
    code: int
    message: str
    data: T | None


class PaginatedAPIResponseSchema(APIResponseSchema, Generic[T]):
    data: list[T]
    total: int
    next: str | None
    previous: str | None


class PageNumberPagination(PageNumberPaginationExtra):
    def get_paginated_response(self, *, base_url: str, page: Any) -> dict:
        return {
            "code": 0,
            "message": "success",
            "data": list(page),
            "total": page.paginator.count,
            "next": self.get_next_link(base_url, page=page),
            "previous": self.get_previous_link(base_url, page=page),
        }

    def paginate_queryset(self, queryset, pagination, request=None, **params):
        try:
            return super().paginate_queryset(queryset, pagination, request=request, **params)
        except NotFound:
            return {
                "code": 0,
                "message": "success",
                "data": [],
                "total": queryset.count(),
                "next": None,
                "previous": None,
            }


def api_response(
    data: str | list | dict | Schema | Model | None = None, message: str = "success", status: int = CODE_SUCCESS
) -> APIResponseSchema:
    return APIResponseSchema(code=status, message=message, data=data)


class DjangoCompatibleEnum(Enum):
    @classmethod
    def from_django_choice(cls, value: Any):
        for member in cls:
            if member.value == value:
                return member
        raise ValueError(f"{value} is not a valid {cls.__name__}")

    def __str__(self):
        return str(self.value)


def choices_to_enum(name, choices: list[tuple[str, str]]) -> Enum:
    """
    Convert Django choices to a Pydantic compatible Enum.

    Args:
    choices (List[Tuple[str, str]]): The Django choices list.

    Returns:
    Enum: A Pydantic compatible Enum class.

    Example:
    >>> STATUS_CHOICES = [
    ...     ('draft', 'Draft'),
    ...     ('published', 'Published'),
    ...     ('archived', 'Archived'),
    ... ]
    >>> StatusEnum = choices_to_enum('StatusEnum', STATUS_CHOICES)
    >>> print(StatusEnum.DRAFT)
    StatusEnum.DRAFT
    >>> print(StatusEnum.DRAFT.value)
    draft
    """
    return DjangoCompatibleEnum(name, {choice[1].replace(" ", "_").upper(): choice[0] for choice in choices})
