# Generated by Django 5.1.1 on 2024-09-19 02:22

import uuid

import django.db.models.deletion
import django_minio_backend.models
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="FileCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="File",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.CharField(help_text="Title of the document.", max_length=255),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to=django_minio_backend.models.iso_date_prefix,
                        verbose_name="File Upload",
                    ),
                ),
                (
                    "file_name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the file.",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "upload_time",
                    models.DateTimeField(auto_now_add=True, help_text="Upload time."),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("EN", "English"),
                            ("ZH", "Chinese"),
                            ("Other", "Other"),
                        ],
                        default="EN",
                        help_text="Language of the document.",
                        max_length=10,
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, help_text="Description of the document.", null=True),
                ),
                (
                    "category",
                    models.ForeignKey(
                        help_text="Category of the document.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="files",
                        to="file.filecategory",
                    ),
                ),
            ],
        ),
    ]
