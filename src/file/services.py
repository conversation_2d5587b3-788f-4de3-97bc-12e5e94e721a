import re

from asgiref.sync import sync_to_async
from celery.result import AsyncResult
from django.core.files.uploadedfile import UploadedFile
from django.db import transaction
from django.db.models import Count
from django.shortcuts import aget_object_or_404, get_object_or_404
from django.utils.translation import gettext as _
from ninja_extra.exceptions import PermissionDenied

from codes import CODE_ALREADY_EXISTS
from exceptions import APIError
from file.models import File, FileCategory
from knowledge import tasks
from knowledge.models import AccidentReport, Chunk
from knowledge.tasks import analysis_accident_text_and_save_report, get_pages_from_file


class FileService:
    def __init__(self, user):
        self.user = user

    @sync_to_async
    def get_categories(self, created_by, name, purpose):
        queryset = FileCategory.objects
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if name:
            filters["name__icontains"] = name
        if purpose:
            filters["purpose"] = purpose
        queryset = queryset.filter(**filters).order_by("-create_time")
        return queryset.annotate(file_count=Count("files"))

    async def get_category(self, category_id):
        category = await aget_object_or_404(FileCategory, id=category_id)
        return category

    async def create_category(self, name, description, purpose):
        if await FileCategory.objects.filter(name=name).aexists():
            raise APIError(_("File category already exists"), code=CODE_ALREADY_EXISTS)
        params = {
            "name": name,
            "description": description,
            "purpose": purpose,
        }
        if not self.user.is_superuser:
            # 非超级用户创建的分类默认为用户分类
            params["purpose"] = FileCategory.PURPOSE_USER
        category = FileCategory(**params)

        if not category.has_create_permission(self.user):
            raise PermissionDenied(_("Permission denied."))

        await category.asave()

        return category

    async def update_category(self, category_id, name=None, description=None, purpose=None):
        category = await aget_object_or_404(FileCategory, id=category_id)

        if not category.has_update_permission(self.user):
            raise PermissionDenied(_("Permission denied."))

        if name and await FileCategory.objects.filter(name=name).exclude(id=category_id).aexists():
            raise APIError(_(f"File category with name {name} already exists"), code=CODE_ALREADY_EXISTS)

        if name is not None:
            category.name = name
        if description is not None:
            category.description = description
        if purpose is not None:
            category.purpose = purpose

        # 需要判断编辑后是否满足权限
        if not category.has_update_permission(self.user):
            raise PermissionDenied(_("Permission denied."))

        await category.asave()
        return category

    async def delete_category(self, category_id):
        category = await aget_object_or_404(FileCategory, id=category_id)

        if not category.has_delete_permission(self.user):
            raise PermissionDenied(_("Permission denied."))

        await category.adelete()
        return True

    def detect_file_type(self, file: UploadedFile):
        if file.content_type.startswith("image"):
            return File.FILE_TYPE_IMAGE

        if file.content_type in [
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
        ]:
            return File.FILE_TYPE_DOC

        if file.content_type.startswith("text"):
            return File.FILE_TYPE_TEXT
        if file.content_type == "application/pdf":
            return File.FILE_TYPE_PDF
        return File.FILE_TYPE_OTHER

    async def upload_file(self, category_id, uploaded_files, check_category=True):
        """上传文件"""
        category = None
        if category_id:
            category = await aget_object_or_404(FileCategory, id=category_id)
        if check_category and category:
            if not category.has_file_upload_permission(self.user):
                raise PermissionDenied(_("Permission denied."))

        files = []
        regex = re.compile(r"(.+)\.\w+$")
        for uploaded_file in uploaded_files:
            file_title = regex.sub(r"\1", uploaded_file.name)
            file = await File.objects.acreate(
                file=uploaded_file,
                title=file_title,
                file_name=uploaded_file.name,
                file_type=self.detect_file_type(uploaded_file),
                file_size=uploaded_file.size,
                category=category,
                created_by=self.user.id,
            )
            files.append(file)
        return files

    @sync_to_async
    def update_files(self, file_infos: list[dict]):
        """上传之后，更新文件信息"""
        files = []
        with transaction.atomic():
            for file_info_dict in file_infos:
                file_id = file_info_dict.pop("file_id")
                file = get_object_or_404(File, id=file_id)
                if not file.category.has_update_permission(self.user):
                    raise PermissionDenied(_("Permission denied."))

                for key, value in file_info_dict.items():
                    setattr(file, key, value)

                file.save()
                files.append(file)
        return files

    async def get_file(self, file_id):
        return await aget_object_or_404(File, id=file_id)

    async def get_file_url(self, file_id):
        file = await aget_object_or_404(File, id=file_id)
        return file.file.url

    @sync_to_async
    def get_files(self, category_id, title, created_by, start_date, end_date, file_type):
        qs = File.objects
        filters = {}
        if category_id:
            filters["category_id"] = category_id
        if title:
            filters["title__icontains"] = title
        if created_by:
            filters["created_by"] = created_by
        if start_date:
            filters["create_time__date__gte"] = start_date
        if end_date:
            filters["create_time__date__lte"] = end_date
        if file_type:
            filters["file_type"] = file_type
        return qs.filter(**filters).order_by("-create_time").all()

    @sync_to_async
    def bulk_delete_file(self, file_ids):
        files = File.objects.filter(id__in=file_ids).select_related("category").all()
        for file in files:
            if not file.category.has_update_permission(self.user):
                raise PermissionDenied(_("Permission denied."))
        File.objects.filter(id__in=file_ids).delete()
        Chunk.objects.filter(file_id__in=file_ids).delete()
        return True

    async def delete_file(self, file_id):
        file = await aget_object_or_404(File.objects.select_related("category"), id=file_id)
        if not file.category.has_update_permission(self.user):
            raise PermissionDenied(_("Permission denied."))
        await file.adelete()
        await Chunk.objects.filter(file_id=file_id).adelete()
        return True

    async def process_category_files(self, category_id):
        category = await aget_object_or_404(FileCategory, id=category_id)
        if not category.has_update_permission(self.user):
            raise PermissionDenied(_("Permission denied."))
        task: AsyncResult = tasks.process_documents.delay(category_id, self.user.id)
        return {"task_id": task.id, "status": task.status}

    async def get_process_status(self, task_id):
        task: AsyncResult = tasks.process_documents.AsyncResult(str(task_id))
        return {"task_id": task.id, "status": task.status, "result": task.result}

    @sync_to_async
    def process_file(self, file_id):
        file = get_object_or_404(File, id=file_id)
        if not file.category.has_update_permission(self.user):
            raise PermissionDenied(_("Permission denied."))
        chunks = tasks.process_file(file)
        return chunks

    @sync_to_async
    def process_file_delay(self, file_id):
        task = tasks.process_file_task.delay(file_id)
        return task

    async def extract_text(
        self, uploaded_file: UploadedFile, save_report: bool = False, report_type: str = AccidentReport.TYPE_EN
    ):
        files = await self.upload_file(0, [uploaded_file], check_category=False)
        file = files[0]
        pages = get_pages_from_file(file)
        all_text = ""
        for page_text, _page_num in pages:
            all_text += page_text
        if save_report:
            await analysis_accident_text_and_save_report(
                file_id=file.id, text=all_text, user_id=self.user.id, save=True, report_type=report_type
            )
        return file, all_text
