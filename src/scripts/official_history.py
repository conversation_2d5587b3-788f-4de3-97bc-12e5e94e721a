#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File    :   official_account_service.py
@Time    :   2024/04/22 18:06:06
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Desc    :   本机爬取历史数据，注意没有使用 scrapy，而且使用的官方公众号平台接口，所以需要登录，并且爬取的结果缺少正文和图片信息
"""

import json
import logging
import random  # type: ignore
import time
from datetime import datetime

from pydantic import HttpUrl

from conf.settings import MINIO_OPTIONS
from conf.settings import OPENGAUSS_OPTIONS
from src.dao.minio.minio import MinioDao
from src.dao.open_gauss.spider_results import SourceResultsDao
from src.storage.open_gauss.connection import get_session
from src.utils.request import get_random_ua, get_retry_request_session
from .official_articles import PublicAccountsWeb

logger = logging.getLogger("crawl")


class OfficialAccountHistoryService(object):
    name = "爬取历史数据服务层，不是爬虫，单独手动调用"
    # 数据保存表名
    save_to_table = "spider.source_results"
    officials = [
        {"target_id": 1, "nickname": "不良资产头条", "fakeid": "MzAwMjU4MDQxNA=="},
        {"target_id": 14, "nickname": "不良资产行业观研", "fakeid": "MzIxOTY1Nzk3MA=="},
        {"target_id": 15, "nickname": "保全部", "fakeid": "MzUwOTE1MTg2MQ=="},
        {"target_id": 2, "nickname": "不良资产大讲坛", "fakeid": "Mzk0MjM0MDQwOQ=="},
        {"target_id": 3, "nickname": "不良资产说", "fakeid": "MzIyNzE2ODcyMg=="},
        {"target_id": 4, "nickname": "券商中国", "fakeid": "MzA3NjM5MjIwOQ=="},
        {"target_id": 5, "nickname": "中国证券报", "fakeid": "MjM5MzMwNjM0MA=="},
        {"target_id": 6, "nickname": "投资界", "fakeid": "MzI5ODk1NjY1MA=="},
        {"target_id": 7, "nickname": "投资家", "fakeid": "MjM5MDAwMjQzNQ=="},
        {"target_id": 8, "nickname": "投中网", "fakeid": "MzkwMjUxNTkwNQ=="},
        {"target_id": 9, "nickname": "保观", "fakeid": "MzI0NjEzMjQ3OQ=="},
        {"target_id": 10, "nickname": "中国信托业协会", "fakeid": "MzA5MTE0NTQwOA=="},
        {"target_id": 11, "nickname": "信托圈内人", "fakeid": "MzU4ODY1MTIxOQ=="},
        {"target_id": 12, "nickname": "北京中关村科技园区西城园协会", "fakeid": "MzU2NjQxMzI5OQ=="},
        {"target_id": 13, "nickname": "金融科技研究", "fakeid": "MzUzOTE1MjcxMA=="},
    ]

    # 进度文件路径
    progress_file_path = "progress_official_history.json"
    count = 5

    def __init__(self, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.minio = MinioDao(**MINIO_OPTIONS)
        self.dao = SourceResultsDao(**OPENGAUSS_OPTIONS)
        self.account = PublicAccountsWeb(uid="default")
        self.logger = logger

        self.target_id = None
        self.begin = 0
        self.nickname = None
        self.fakeid = None
        self.finished = False

    def login(self):
        self.account.login()

    def logout(self):
        return self.account.logout()

    def save_cookies(self):
        return self.account.save_cookies()

    def get_token(self):
        return self.account.get_token()

    def get_urls(self, nickname=None, biz=None, begin=0, count=5):
        return self.account.get_urls(nickname, biz, begin, count)

    def do_request(self) -> json:
        """
        step1
        请求公众号接口
        注意事项：
        想要请求成功， 注意两个点：
        1. 请求头中，需要设置 cookie
        2. 请求参数中，需要设置 token 参数需要设置
        token 来源： 微信公从号的登录页面，参考：https://mp.weixin.qq.com/cgi-bin/home?t=home/index&lang=zh_CN&token=**********
        @Parameters  :

        @Returns :

        """

        self.logger.info(f"公众号：{self.nickname}")

        self.login()
        result = self.get_urls(biz=self.fakeid, begin=self.begin, count=self.count)

        self.logger.info(
            f"公众号: {self.nickname}，biz: {self.fakeid}, begin: {self.begin}, count: {self.count}, 结果为: {result['base_resp']}"
        )

        return result

    def parse_response(self, response_json: json) -> list:
        """
        step2
        解析公众号的响应结果
        输入：解析接口响应的json数据，
        输出：公众号发布的文章列表
        """
        ret = response_json["base_resp"]["ret"]
        if ret == 0:
            self.logger.info(f"total: {response_json['app_msg_cnt']}")
            if self.begin > response_json["app_msg_cnt"] or not response_json["app_msg_list"]:
                self.finished = True

            return response_json["app_msg_list"]

        # 出错则直接结束
        raise Exception(
            f"公众号：{self.nickname}，biz：{self.fakeid}，请求失败，错误信息：{response_json['base_resp']['err_msg']}"
        )

    def get_require_field(
        self,
        article_list: json,
    ) -> list:
        """
        step3
        基于原始信息，基于业务需求取出所需要的字段信息，如title， link, publish_time 等信息
        输入：公众号发布的文章列表
        输出：所需要的字段及值，用于下一步写入数据库和对象存储
        """
        result = []
        for source in article_list:
            row = {}
            row["target_id"] = self.target_id
            row["target_category"] = 1
            row["source_aid"] = source["aid"]
            row["source_appmsgid"] = source["appmsgid"]
            row["source_author_name"] = source.get("author_name", "")  # FIXME 目前拿不到作者名字
            row["source_title"] = source["title"]
            row["source_link"] = source["link"]
            row["source_link_detail_path"] = self.get_link_detail(source["aid"], source["link"])
            row["source_create_time"] = self.get_std_timeformat(source["create_time"])
            row["source_update_time"] = self.get_std_timeformat(source["update_time"])
            row["source_info"] = source
            result.append(row)
            time.sleep(1)
        return result

    def get_std_timeformat(self, timestamp):
        return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")

    def get_link_detail(self, aid: str, link: HttpUrl) -> str:
        """
        获取公众号文章的内容，将内容保存到 minio 中， 返回 minio 的地址
        """
        session = get_retry_request_session()
        response = session.get(link, headers={"user-agent": get_random_ua()})

        file_name = f"official_account/{self.fakeid}/{aid}.html"
        if response.status_code == 200:
            content = response.text
            self.minio.put_object(object_name=file_name, data=content)
        return file_name

    def save_to_database(self, require_data):
        """
        step4
        所需要的字段及值保存至数据库中
        """
        self.logger.info("开始保存数据至数据库")
        with get_session() as db:
            result = self.dao.insert_to_table(db, require_data, on_duplicate_key_update_nothing=True)

        self.logger.info(f"save into the database finished")
        return result

    def save_progress(self, progress):
        progress[self.nickname] = {
            "begin": self.begin,
            "finished": self.finished,
        }

        with open(self.progress_file_path, "w") as f:
            json.dump(progress, f)

        self.save_cookies()

    def run(self):
        # 读取之前进度
        progress = {}
        try:
            with open(self.progress_file_path, "r") as f:
                progress = json.load(f)
        except:
            pass

        for official in self.officials:
            self.nickname = official["nickname"]
            self.fakeid = official["fakeid"]
            self.target_id = official["target_id"]
            self.begin = 0
            if self.nickname in progress.keys():
                if progress[self.nickname].get("finished", False):
                    continue
                self.begin = progress[self.nickname].get("begin", 0)

            self.finished = False

            while not self.finished:
                try:
                    # step1: 爬取公众号的文章列表
                    step1_result = self.do_request()
                    # step2： 解板公众号的文章列表
                    step2_result = self.parse_response(step1_result)
                except Exception as e:
                    if "invalid session" in str(e):
                        # 登录失效，登出然后从新登入
                        self.logout()
                        continue
                    if "freq control" in str(e):
                        # 触发了频率控制，休息一小时，再加一点以防万一
                        time.sleep(3700)
                        continue
                    raise e

                if not step2_result:
                    self.save_progress(progress)
                    continue

                # step3： 从文章列表中，获取每个文章需要的字段及文章内容
                step3_result = self.get_require_field(step2_result)
                # step4： 每个文章的字段信息都保存到数据库中
                step4 = self.save_to_database(step3_result)

                # 更新进度
                self.begin = self.begin + self.count
                self.save_progress(progress)

    def get_total_all(self):
        for official in self.officials:
            self.nickname = official["nickname"]
            self.fakeid = official["fakeid"]
            self.target_id = official["target_id"]
            result = self.do_request()
            self.logger.info("%s, %s, %s", self.nickname, self.fakeid, result.get("app_msg_cnt"))

            time.sleep(1)


if __name__ == "__main__":
    service = OfficialAccountHistoryService()
    service.run()
