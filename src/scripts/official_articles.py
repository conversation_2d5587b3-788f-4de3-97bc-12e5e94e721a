# coding:  utf-8
import hashlib
import os
import time

import requests
from http import cookiejar as cookielib


def current_milli_time():
    return round(time.time() * 1000)


class PublicAccountsWeb(object):
    """通过微信公众号网页版抓取链接，或者公众号信息"""

    def __init__(self, cookie=None, token=None, uid=None, proxies=None):
        """
        Parameters
        ----------
        token : str
            登录微信公众号平台之后获取的token
        cookie : str
            登录微信公众号平台之后获取的cookie
        """
        if proxies is None:
            proxies = {"http": None, "https": None}
        self.s = requests.session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.62 Safari/537.36"
        }
        self.params = {
            "lang": "zh_CN",
            "f": "json",
        }

        # 手动输入cookie和token登录
        if cookie:
            self.__verify_str(cookie, "cookie")
            self.headers["Cookie"] = cookie
        if token:
            self.__verify_str(token, "token")
            self.params["token"] = token

        self.uid = uid

        if not (cookie and token):
            self.logged_uid = None

        if uid:
            try:
                self.__read_cookie(uid)
                self.logged_uid = uid
            except:
                pass

        self.proxies = proxies

    def __verify_str(self, input_string, param_name):
        """
        验证输入是否为字符串

        Parameters
        ----------
        input_string: str
            输入
        param_name: str
            需要验证的参数名
        """
        if not isinstance(input_string, str):
            raise TypeError("{} must be an instance of str".format(param_name))

    def __save_login_qrcode(self, img):
        """
        存储和显示登录二维码

        Parameters
        ----------
        img: Any
            获取到的二维码数据
        """
        import matplotlib.pyplot as plt
        from PIL import Image

        # 存储二维码
        with open("login.png", "wb+") as fp:
            fp.write(img.content)
        # 显示二维码， 这里使用plt的原因是： 等待用户扫描完之后手动关闭窗口继续运行；否则会直接运行
        try:
            img = Image.open("login.png")
        except Exception:
            raise TypeError("账号密码输入错误，请重新输入")
        plt.figure()
        plt.imshow(img)
        plt.show(block=True)

    def __save_cookie(self, uid=None, token=None):
        """
        存储cookies, name用于文件命名

        Parameters
        ----------
        uid: str
            用户账号，这里暂时使用 cookies 里面的 bizuin
        """
        if not token:
            token = self.params.get("token", "")
        if not uid:
            uid = self.uid or self.s.cookies.get("bizuin", "default_name")

        os.makedirs("cookies", exist_ok=True)
        # 实例化一个LWPcookiejar对象
        new_cookie_jar = cookielib.LWPCookieJar(uid + ".txt")

        # 将转换成字典格式的RequestsCookieJar（这里我用字典推导手动转的）保存到LWPcookiejar中
        cookies_dict = {c.name: c.value for c in self.s.cookies}
        if token:
            cookies_dict["token"] = token
        requests.utils.cookiejar_from_dict(cookies_dict, new_cookie_jar)

        # 保存到本地文件
        new_cookie_jar.save("cookies/" + uid + ".txt", ignore_discard=True, ignore_expires=True)
        self.logged_uid = uid

    def __read_cookie(self, uid=None):
        """
        读取cookies, name用于文件命名

        Parameters
        ----------
        uid: str
            用户账号，这里暂时使用 cookies 里面的 bizuin
        """
        if not uid:
            uid = self.uid or self.s.cookies.get("bizuin", "default_name")

        # 实例化一个LWPCookieJar对象
        load_cookiejar = cookielib.LWPCookieJar()
        # 从文件中加载cookies(LWP格式)
        load_cookiejar.load("cookies/" + uid + ".txt", ignore_discard=True, ignore_expires=True)
        # 工具方法转换成字典
        load_cookies = requests.utils.dict_from_cookiejar(load_cookiejar)
        # 工具方法将字典转换成RequestsCookieJar，赋值给session的cookies.
        self.s.cookies = requests.utils.cookiejar_from_dict(load_cookies)
        self.params["token"] = self.s.cookies.get("token", "")

    def __md5_passwd(self, password):
        """
        微信公众号的登录密码需要用md5方式进行加密

        Parameters
        ----------
        password: str
            加密前的字符串

        Returns
        -------
        str：
            加密后的字符串
        """
        m5 = hashlib.md5()
        m5.update(password.encode("utf-8"))
        pwd = m5.hexdigest()
        return pwd

    def __startlogin_official(self, uid=None):
        """
        获取登录二维码，进而获取Cookies

        Parameters
        ----------

        Returns
        -------
            None
        """

        # 增加headers的keys
        self.headers["Host"] = "mp.weixin.qq.com"
        self.headers["Origin"] = "https://mp.weixin.qq.com"
        self.headers["Referer"] = "https://mp.weixin.qq.com/"

        # 开始登录以及设置 cookie 的 url
        login_url = "https://mp.weixin.qq.com/cgi-bin/bizlogin?action=startlogin"
        self.s.post(
            url=login_url,
            headers=self.headers
            | {
                "Content-Type": "text/plain; charset=utf-8",
            },
            data="userlang=zh_CN&redirect_url=&login_type=3&token=&lang=zh_CN&f=json&ajax=1",
            proxies=self.proxies,
        )

        self.__show_qrcode()

        # 去除之后不用的headers的key
        self.headers.pop("Host")
        self.headers.pop("Origin")
        # 获取token
        self.__login_official(uid)

    def __show_qrcode(self):

        # 获取二维码的url
        qrcode_url = "https://mp.weixin.qq.com/cgi-bin/scanloginqrcode?action=getqrcode&random={}".format(
            current_milli_time()
        )

        # 账号密码登录，获取二维码，等待用户扫描二维码，需手动关闭二维码窗口
        img = self.s.get(qrcode_url, headers=self.headers, proxies=self.proxies)
        self.__save_login_qrcode(img)

    def __login_official(self, uid=None):
        """
        登录微信公众号平台，获取token

        Parameters
        ----------
        """
        self.headers["Referer"] = "https://mp.weixin.qq.com/"

        # 获取token的data
        bizlogin_url = "https://mp.weixin.qq.com/cgi-bin/bizlogin?action=login"
        res = self.s.post(
            bizlogin_url,
            data="userlang=zh_CN&redirect_url=&cookie_forbidden=0&cookie_cleaned=1"
            "&plugin_used=0&login_type=3&token=&lang=zh_CN&f=json&ajax=1",
            headers=self.headers
            | {
                "Content-Type": "text/plain; charset=utf-8",
            },
            proxies=self.proxies,
        ).json()

        # 截取字符串中的token参数
        token = res["redirect_url"].split("=")[-1]
        self.params["token"] = token
        self.__save_cookie(uid, token=token)
        self.headers.pop("Referer")

    def official_info(self, nickname, begin=0, count=5):
        """
        根据关键词返回相关公众号的信息

        Parameters
        ----------
        nickname : str
            需要爬取公众号名称
        begin: str or int
            起始爬取的页数
        count: str or int
            每次爬取的数量，1-5

        Returns
        -------
        list:
            相关公众号的对应信息::

                [
                    {
                    'alias': 公众号别名,
                    'fakeid': 公众号唯一id,
                    'nickname': 公众号名称,
                    'round_head_img': 公众号头像的url,
                    'service_type': 1公众号性质
                    },
                ...
                ]
        """
        self.__verify_str(nickname, "nickname")
        # 搜索公众号的url
        search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"

        # 增加/更改请求参数
        params = {
            "query": nickname,
            "count": str(count),
            "action": "search_biz",
            "ajax": "1",
            "begin": str(begin),
        }
        self.params.update(params)

        try:
            # 返回与输入公众号名称最接近的公众号信息
            official = self.s.get(
                search_url,
                headers=self.headers,
                params=self.params,
                proxies=self.proxies,
            )
            return official.json()["list"]
        except Exception:
            raise Exception("公众号名称错误或cookie、token错误，请重新输入")

    def articles_nums(self, nickname):
        """
        获取公众号的总共发布的文章数量

        Parameters
        ----------
        nickname : str
            需要爬取公众号名称

        Returns
        -------
        int
            文章总数
        """
        self.__verify_str(nickname, "nickname")
        try:
            return self.__get_articles_data(nickname, begin="0")["app_msg_cnt"]
        except Exception:
            raise Exception("公众号名称错误或cookie、token错误，请重新输入")

    def get_urls(self, nickname=None, biz=None, begin=0, count=5):
        """
        获取公众号的每页的文章信息

        Parameters
        ----------
        nickname : str
            需要爬取公众号名称

        biz : str
            需要爬取公众号的biz, 优先

        begin: str or int
            起始爬取的文章数

        count: str or int
            每次爬取的数量，1-5

        Returns
        -------
        list:
            由每个文章信息构成的数组::

                [
                {
                    'aid': '2650949647_1',
                    'appmsgid': 2650949647,
                    'cover': 封面的url'digest': 文章摘要,
                    'itemidx': 1,
                    'link': 文章的url,
                    'title': 文章标题,
                    'update_time': 更新文章的时间戳
                },
                ]
            如果list为空则说明没有相关文章
        """

        try:
            return self.__get_articles_data(nickname, biz, begin=str(begin), count=str(count))
        except Exception:
            raise Exception("公众号名称错误或cookie、token错误，请重新输入")

    def latest_articles(self, biz):
        """
        获取公众号的最新页的文章信息

        Parameters
        ----------
        biz : str
            公众号的biz

        Returns
        -------
        list:
            由每个文章信息构成的数组::

                [
                {
                    'aid': '2650949647_1',
                    'appmsgid': 2650949647,
                    'cover': 封面的url'digest': 文章摘要,
                    'itemidx': 1,
                    'link': 文章的url,
                    'title': 文章标题,
                    'update_time': 更新文章的时间戳
                },
                ]
            如果list为空则说明没有相关文章
        """
        try:
            return self.__get_articles_data("", begin="0", biz=biz)
        except Exception:
            raise Exception("公众号名称错误或cookie、token错误，请重新输入")

    def __get_articles_data(
        self,
        nickname,
        biz,
        begin,
        count=5,
        type_="9",
        action="list_ex",
        query=None,
    ):
        """
        Parameters
        ----------
        nickname : str
            需要爬取公众号名称
        biz : str
            公众号的biz
        begin: str or int
            起始爬取的页数
        count: str or int
            每次爬取的数量，1-5
        type_: str or int
            获取数据的方式，暂不知道具体用途
        action: str
            请求之后的行为动作，"list_ex"获取文章信息的json
        Returns
        -------
        json:
            文章信息的json::

                {
                'app_msg_cnt': 公众号发文章总数,
                'app_msg_list': 　一个数组(参看get_articles函数),
                'base_resp': {
                    'err_msg': 'ok',
                    'ret': 0
                }
                }
        """
        # 获取文章信息的url
        appmsg_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"

        try:
            if biz:
                self.params["fakeid"] = biz
            if nickname:
                # 获取公众号的fakeid
                official_info = self.official_info(nickname)
                self.params["fakeid"] = official_info[0]["fakeid"]
        except Exception:
            raise Exception("公众号名称错误或cookie、token错误，请重新输入")

        # 增加/更改请求参数
        params = {
            "query": query if query != None else "",
            "begin": str(begin),
            "count": str(count),
            "type": str(type_),
            "action": action,
        }
        self.params.update(params)

        data = self.s.get(appmsg_url, headers=self.headers, params=self.params, proxies=self.proxies)
        return data.json()

    def is_logged(self, uid):
        return self.logged_uid or (uid and self.logged_uid == uid)

    def login(self, uid=None):
        if not self.is_logged(uid):
            self.__startlogin_official()

    def logout(self, uid=None):
        if self.is_logged(uid):
            self.clear_cookies(uid)
            self.clear_token(uid)
            self.logged_uid = None

    def get_cookies(self, uid=None):
        if not self.is_logged(uid):
            try:
                self.__read_cookie(uid)
            except:
                self.__startlogin_official()
        return self.s.cookies

    def save_cookies(self, uid=None, token=None):
        self.__save_cookie(uid, token)

    def clear_cookies(self, uid=None):
        if self.is_logged(uid):
            self.s.cookies.clear()

    def get_token(self, uid=None):
        if not self.is_logged(uid):
            self.__startlogin_official()
        return self.params.get("token")

    def clear_token(self, uid=None):
        if self.is_logged(uid):
            self.params.pop("token", None)
