"""
处理历史数据

从 official_history.py 爬取的历史数据，缺少图片和正文信息，以及接口信息格式和目前使用的极致了格式不一样
所以需要手动从数据库里面提取出来并且使用 scrapy 再爬取一次

事先修改历史数据，使得 aid 不会和已有的冲突

select * from spider.mem_spider_results msr where content = '';
select count(1) from spider.mem_spider_results msr where content = '';

update spider.mem_spider_results msr set msr.source_aid = msr.source_aid + '_old' where content = '';

select * from spider.mem_spider_results msr where msr.source_aid like '%_old' and content = '';
select count(1) from spider.mem_spider_results msr where msr.source_aid like '%_old' and content = '';

delete from spider.mem_spider_results msr where msr.source_aid like '%_old' and content = '';

"""

import logging
from multiprocessing import Process

from scrapy.crawler import CrawlerRunner
from scrapy.utils.project import get_project_settings
from twisted.internet import reactor

from spider.spiders.official_account import OfficialAccountSpider
from src.dao.open_gauss.spider_results import SourceResultsDao
from src.dao.open_gauss.spider_targets import TargetTableDao
from src.services.jizhile import JiZhiLe
from src.services.kuaidaili import KDL
from src.storage.open_gauss.connection import get_session

logger = logging.getLogger("crawl")


class CrawlProcess(Process):
    def __init__(self, spider, **kwargs):
        super().__init__()
        self.spider = spider
        self.kwargs = kwargs

    def run(self):
        # 使用自己的代理
        KDL.get_proxies = get_proxies
        settings = get_project_settings()
        settings.setdict(
            {
                "DOWNLOAD_DELAY": 0,
                "TWISTED_REACTOR": None,
            }
        )
        crawler = CrawlerRunner(settings)
        crawler.crawl(self.spider, **self.kwargs)
        d = crawler.join()
        d.addBoth(lambda _: reactor.stop())
        reactor.run(0)


def get_proxies(*args, **kwargs):
    # FIXME 改成能长期使用的代理
    return [
        "t12110895836331:<EMAIL>:15818",
        "t12110895836331:<EMAIL>:15818",
    ]


def run_spider(infos, target_id, fakeid):
    p = CrawlProcess(
        OfficialAccountSpider,
        infos=infos,
        target_id=target_id,
        fakeid=fakeid,
        task_id="",
    )
    p.start()
    p.join()


def main():
    target_dao = TargetTableDao()
    result_dao = SourceResultsDao()
    with get_session() as session:
        target_list = target_dao.get_target_list(session)
    for target in target_list:
        # 获取数据库里面所有正文为空的数据，也就是之前爬取的历史数据
        with get_session() as session:
            total, result = result_dao.get_data(session, None, None, content="", target_id=target["id"])
        if not total:
            continue

        for i in range(0, total, 100):
            total, result = result_dao.get_data(
                session, None, None, offset=i, count=100, content="", target_id=target["id"]
            )
            logger.info(
                f"开始爬取 {target['id']} {target['nickname']} 的历史数据 {i} of {total}, 实际条数{len(result)}"
            )

            if not result:
                break

            infos = []
            for item in result:
                if item.source_info.get("itemidx"):
                    infos.append(JiZhiLe.from_wechat_public(item.source_info))
                else:
                    infos.append(item.source_info)

            aids = [JiZhiLe.get_aid(info) for info in infos]
            with get_session() as db:
                non_exist_aids = set(result_dao.filter_exist_aids(db, target["id"], aids))

            data_to_crawl = [
                info for info in infos if JiZhiLe.get_aid(info) in non_exist_aids
            ]  # 过滤掉已经爬取过的文章

            if data_to_crawl:
                run_spider(infos=data_to_crawl, target_id=target["id"], fakeid=target["fakeid"])


if __name__ == "__main__":
    main()
