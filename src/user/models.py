from enum import Enum

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from pydantic import computed_field


class UserRole(Enum):
    ADMIN = "admin"
    STAFF = "staff"
    USER = "user"


# 部门
class Department(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey("self", on_delete=models.SET_NULL, null=True, blank=True)
    create_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Department")
        verbose_name_plural = _("Department")
        ordering = ["-id"]

    def __str__(self):
        return f"{self.name}"


# 用户
class User(AbstractUser):
    department = models.ForeignKey(
        Department,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="users",
        verbose_name=_("Department"),
    )
    phone = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return f"{self.username}"

    class Meta:
        ordering = ["-id"]

    @computed_field
    @property
    def role(self) -> UserRole:
        if self.is_superuser:
            return UserRole.ADMIN
        if self.is_staff:
            return UserRole.STAFF
        return UserRole.USER
