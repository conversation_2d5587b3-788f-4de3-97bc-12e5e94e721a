import datetime
import json
import logging
import re
from typing import Any, Callable

import dateparser
import zoneinfo
from django.conf import settings
from tokenizers import Tokenizer

local_timezone = zoneinfo.ZoneInfo("Asia/Shanghai")

logger = logging.getLogger(__name__)


def normalize_time(time, tz=local_timezone, raise_exception=False):
    dt = datetime.datetime.now(tz=tz)
    if isinstance(time, str):
        if time.isdecimal():
            time = int(time)
            dt = datetime.datetime.fromtimestamp(time, tz=tz)
        else:
            try:
                parsed = dateparser.parse(time, settings={"TIMEZONE": tz.key})
                if parsed:
                    dt = parsed
            except TypeError as ex:
                # 无法解析
                logger.error(f"无法解析时间：{time}")
                if raise_exception:
                    raise ValueError(f"无法解析时间：{time}") from ex
    elif isinstance(time, (datetime.datetime, datetime.date, datetime.time)):
        dt = time
    else:
        if raise_exception:
            # 无法解析
            logger.error(f"无法解析时间：{time}")
            raise ValueError(f"无法解析时间：{time}")

    return dt.strftime("%Y-%m-%d %H:%M:%S")


def normalize_date(date, tz=local_timezone, raise_exception=False):
    dt = normalize_time(date, tz, raise_exception)
    return dt.split()[0]


# Adapted from https://github.com/KillianLucas/open-interpreter/blob/5b6080fae1f8c68938a1e4fa8667e3744084ee21/interpreter/utils/parse_partial_json.py
# MIT License


def parse_partial_json(s: str, *, strict: bool = False) -> Any:
    """Parse a JSON string that may be missing closing braces.

    Args:
        s: The JSON string to parse.
        strict: Whether to use strict parsing. Defaults to False.

    Returns:
        The parsed JSON object as a Python dictionary.
    """
    # Attempt to parse the string as-is.
    try:
        return json.loads(s, strict=strict)
    except json.JSONDecodeError:
        pass

    # Initialize variables.
    new_chars = []
    stack = []
    is_inside_string = False
    escaped = False

    # Process each character in the string one at a time.
    for char in s:
        if is_inside_string:
            if char == '"' and not escaped:
                is_inside_string = False
            elif char == "\n" and not escaped:
                char = "\\n"  # Replace the newline character with the escape sequence.
            elif char == "\\":
                escaped = not escaped
            else:
                escaped = False
        else:
            if char == '"':
                is_inside_string = True
                escaped = False
            elif char == "{":
                stack.append("}")
            elif char == "[":
                stack.append("]")
            elif char == "}" or char == "]":
                if stack and stack[-1] == char:
                    stack.pop()
                else:
                    # Mismatched closing character; the input is malformed.
                    return None

        # Append the processed character to the new string.
        new_chars.append(char)

    # If we're still inside a string at the end of processing,
    # we need to close the string.
    if is_inside_string:
        new_chars.append('"')

    # Reverse the stack to get the closing characters.
    stack.reverse()

    # Try to parse mods of string until we succeed or run out of characters.
    while new_chars:
        # Close any remaining open structures in the reverse
        # order that they were opened.
        # Attempt to parse the modified string as JSON.
        try:
            return json.loads("".join(new_chars + stack), strict=strict)
        except json.JSONDecodeError:
            # If we still can't parse the string as JSON,
            # try removing the last character
            new_chars.pop()

    # If we got here, we ran out of characters to remove
    # and still couldn't parse the string as JSON, so return the parse error
    # for the original string.
    return json.loads(s, strict=strict)


_json_strip_chars = " \n\r\t`"


def _replace_new_line(match: re.Match[str]) -> str:
    value = match.group(2)
    value = re.sub(r"\n", r"\\n", value)
    value = re.sub(r"\r", r"\\r", value)
    value = re.sub(r"\t", r"\\t", value)
    value = re.sub(r'(?<!\\)"', r"\"", value)

    return match.group(1) + value + match.group(3)


def _custom_parser(multiline_string: str) -> str:
    """
    The LLM response for `action_input` may be a multiline
    string containing unescaped newlines, tabs or quotes. This function
    replaces those characters with their escaped counterparts.
    (newlines in JSON must be double-escaped: `\\n`)
    """
    if isinstance(multiline_string, (bytes, bytearray)):
        multiline_string = multiline_string.decode()

    multiline_string = re.sub(
        r'("action_input"\:\s*")(.*?)(")',
        _replace_new_line,
        multiline_string,
        flags=re.DOTALL,
    )

    return multiline_string


def _parse_json(json_str: str, *, parser: Callable[[str], Any] = parse_partial_json) -> dict:
    # Strip whitespace,newlines,backtick from the start and end
    json_str = json_str.strip(_json_strip_chars)

    # handle newlines and other special characters inside the returned value
    json_str = _custom_parser(json_str)

    # Parse the JSON string into a Python dictionary
    return parser(json_str)


_json_markdown_re = re.compile(r"```(json)?(.*)", re.DOTALL)


def parse_json_markdown(json_string: str, *, parser: Callable[[str], Any] = parse_partial_json) -> dict:
    """Parse a JSON string from a Markdown string.

    Args:
        json_string: The Markdown string.

    Returns:
        The parsed JSON object as a Python dictionary.
    """
    try:
        return _parse_json(json_string, parser=parser)
    except json.JSONDecodeError:
        # Try to find JSON string within triple backticks
        match = _json_markdown_re.search(json_string)

        # If no match found, assume the entire string is a JSON string
        if match is None:
            json_str = json_string
        else:
            # If match found, use the content within the backticks
            json_str = match.group(2)
    return _parse_json(json_str, parser=parser)


def extract_codes(cause_code):
    four_digit_regex = re.compile(r"(\d{4})")
    two_digit_regex = re.compile(r"(\d{2})")
    four_digit_match = four_digit_regex.search(cause_code)
    four_digit_code = four_digit_match.group(1) if four_digit_match else "0"

    if four_digit_code:
        two_digit_code = four_digit_code[:2]
    else:
        two_digit_match = two_digit_regex.search(cause_code)
        two_digit_code = two_digit_match.group(1) if two_digit_match else "0"

    return int(two_digit_code), int(four_digit_code)


BASE_DIR = settings.BASE_DIR
# 默认使用千问系列的 tokenizer，需要离线支持第三方的 tokenizer
QWEN_TOKENIZER = Tokenizer.from_file((BASE_DIR / "prebuilt/tokenizer/qwen2.5/tokenizer.json").resolve().as_posix())
ENDING_PERCENTAGE = settings.OPENAI_SPLIT_ENDING_PERCENTAGE


def get_token_num(text, tokenizer=QWEN_TOKENIZER):
    """
    Get the number of tokens for a given text using the provided tokenizer.

    Args:
        text (str): The text to be tokenized.
        tokenizer (huggingface.transformers.PreTrainedTokenizer, optional):
            The tokenizer to use. Defaults to QWEN_TOKENIZER.

    Returns:
        int: The number of tokens in the given text.
    """
    return len(tokenizer.encode(text).ids)


def truncate_text_by_tokens(text, max_tokens, direction="start", tokenizer=QWEN_TOKENIZER):
    """
    Truncate a given text to a maximum number of tokens in either direction.

    Args:
        text (str): The text to be truncated.
        max_tokens (int): The maximum number of tokens to truncate to.
        direction (str, optional): The direction of the truncation. Defaults to "start".
            Options: "start" - truncate from the start, "end" - truncate from the end.
        tokenizer (huggingface.transformers.PreTrainedTokenizer, optional): The tokenizer to use.
            Defaults to `QWEN_TOKENIZER`.

    Returns:
        str: The truncated text.
    """
    if max_tokens <= 0:
        return ""
    encoded = tokenizer.encode(text)
    stride = min(100, len(encoded), max_tokens - 1)
    if direction == "start":
        encoded.truncate(max_tokens, stride, "right")
    elif direction == "end":
        encoded.truncate(max_tokens, stride, "left")
    return tokenizer.decode(encoded.ids)


def truncate_long_text(
    text,
    max_tokens=None,
    extra_text="",
    response_tokens=1000,
    ending_percentage=ENDING_PERCENTAGE,
    tokenizer=QWEN_TOKENIZER,
    llm_api=None,
):
    """
    因为大模型的 token 有限，所以需要对 text 进行截断，token 包含 prompt 和 response，
    预留 response_tokens 的长度给回复，
    先计算 extra_text 的 token 数，这部分是必须使用的长度，使用 max_tokens 减去这部分，再计算剩下的部分
    剩下的长度，根据 ending_percentage 百分比截取后面的部分， 然后再拼接剩余百分比截取的前面的部分
    """
    if not max_tokens:
        llm_api_config = settings.LLM_APIS.get(llm_api, settings.LLM_APIS["default"])
        max_tokens = llm_api_config["max_tokens"]
    extra_tokens = get_token_num(extra_text, tokenizer=tokenizer)
    total_tokens = max_tokens - extra_tokens - response_tokens
    if total_tokens <= 0:
        return ""
    end_tokens = int(ending_percentage * total_tokens)

    truncated_text = truncate_text_by_tokens(text, total_tokens - end_tokens, direction="start", tokenizer=tokenizer)
    ending_text = truncate_text_by_tokens(text, end_tokens, direction="end", tokenizer=tokenizer)
    return truncated_text + "\n" + ending_text
