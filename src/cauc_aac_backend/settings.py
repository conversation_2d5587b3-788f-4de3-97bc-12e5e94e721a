"""
Django settings for cauc_aac_backend project.

Generated by 'django-admin startproject' using Django 5.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from collections import OrderedDict
from datetime import timedelta
from pathlib import Path

from environs import Env

env = Env()
env.read_env()  # read .env file, if it exists

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env.str("SECRET_KEY", "&$l9+=-^-5*ej0dhxn9trzh-t+%0ojntb3vhnxdxeyqownw8q^")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DEBUG", default=False)

VERSION = env.str("VERSION", "debug")

ALLOWED_HOSTS = ["*"]


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # Third-party apps
    "corsheaders",
    "constance",
    "ninja",
    "ninja_extra",
    "ninja_jwt",
    "ninja_jwt.token_blacklist",
    "django_minio_backend",
    "django_neomodel",
    "captcha",
    # Local apps
    "file",
    "graph",
    "user",
    "knowledge",
    "frontend",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "cauc_aac_backend.urls"


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "cauc_aac_backend.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": env.str("DB_ENGINE", "django.db.backends.sqlite3"),
        "NAME": env("DB_NAME", BASE_DIR / "db.sqlite3"),
        "USER": env.str("DB_USER", ""),
        "PASSWORD": env.str("DB_PASSWORD", ""),
        "HOST": env.str("DB_HOST", "localhost"),
        "PORT": env.str("DB_PORT", "5432"),
    },
    "OPTIONS": {
        "pool": True,
    },
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Password hashers
# https://docs.djangoproject.com/en/5.1/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
    "django.contrib.auth.hashers.ScryptPasswordHasher",
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True

# 配置可用的语言
LANGUAGES = [
    ("en", "English"),
    ("zh-hans", "Simplified Chinese"),  # 中文翻译
    # 其他语言
]

# 设置翻译文件存放路径
LOCALE_PATHS = [
    BASE_DIR / "locale",
    BASE_DIR / "locale_override",
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = "static/"

STATIC_ROOT = BASE_DIR / "static"

STATICFILES_DIRS = [
    BASE_DIR / "statics",
]

STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# https://whitenoise.readthedocs.io/en/latest/django.html
# 正常情况下，django 在生产环境下不会处理静态文件，需要使用 ./manage.py collectstatic，再用 nginx 等 web 服务器来处理静态文件
# 为了简单可以使用 whitenoise 来处理静态文件
STORAGES = {
    "default": {
        "BACKEND": "django_minio_backend.models.MinioBackend",
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

AUTH_USER_MODEL = "user.User"


AUTHENTICATION_BACKENDS = [
    # django admin 后台鉴权方式，优先级从上到下
    "user.authentication.UserBackend",
]

# https://github.com/adamchainz/django-cors-headers
# 允许跨站访问的域名列表，方便本地前端跨站调试
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8000",
    "http://localhost:8012",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:8012",
]
CORS_EXTRA_ALLOWED_ORIGINS = env.str("CORS_EXTRA_ORIGINS", "")
if CORS_EXTRA_ALLOWED_ORIGINS:
    CORS_ALLOWED_ORIGINS += CORS_EXTRA_ALLOWED_ORIGINS.split(",")
CORS_ALLOW_CREDENTIALS = True

# https://docs.djangoproject.com/en/5.1/ref/settings/#secure-proxy-ssl-header
# 安全请求头，防止代理（如 nginx）篡改请求头
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
# https://docs.djangoproject.com/en/5.1/ref/settings/#csrf-trusted-origins
# CSRF 相关设置，当来源(主要是非http)在这个列表里面的时候，不需要 CSRF 验证
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:8000",
    "http://localhost:8012",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:8012",
    "http://************:8012/",
    "http://cauc-dev:8012/",
]
CSRF_EXTRA_TRUSTED_ORIGINS = env.str("CSRF_EXTRA_ORIGINS", "")
if CSRF_EXTRA_TRUSTED_ORIGINS:
    CSRF_TRUSTED_ORIGINS += CSRF_EXTRA_TRUSTED_ORIGINS.split(",")

MINIO_ENDPOINT = env.str("MINIO_ENDPOINT", "localhost:9000")
MINIO_EXTERNAL_ENDPOINT = env.str("MINIO_EXTERNAL_ENDPOINT", "127.0.0.1:9000")
MINIO_USE_HTTPS = env.bool("MINIO_USE_HTTPS", False)
MINIO_EXTERNAL_ENDPOINT_USE_HTTPS = env.bool("MINIO_EXTERNAL_ENDPOINT_USE_HTTPS", False)
MINIO_ACCESS_KEY = env.str("MINIO_ACCESS_KEY", "")
MINIO_SECRET_KEY = env.str("MINIO_SECRET_KEY", "")
MINIO_CONSISTENCY_CHECK_ON_START = env.bool("MINIO_CONSISTENCY_CHECK_ON_START", True)
MINIO_MEDIA_FILES_BUCKET = "cauc-incidents-media"
MINIO_PRIVATE_BUCKETS = [
    "cauc-incidents-private",
    "cauc-incidents-media",
]
MINIO_PUBLIC_BUCKETS = [
    "cauc-incidents-public",
]
MINIO_URL_EXPIRY_HOURS = timedelta(hours=env.int("MINIO_URL_EXPIRY_HOURS", 24))

NEOMODEL_NEO4J_BOLT_URL = env.str("NEOMODEL_NEO4J_BOLT_URL", "bolt://localhost:7687")


REDIS_URL = env.str("REDIS_URL", "")
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

NINJA_PAGINATION_PER_PAGE = env.int("PAGINATION_PER_PAGE", 20)

# https://eadwincode.github.io/django-ninja-jwt/settings/
# JWT 配置, 用于用户登录认证
NINJA_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=env.int("JWT_TOKEN_LIFETIME", 5)),  # 5 minutes
    "REFRESH_TOKEN_LIFETIME": timedelta(minutes=env.int("JWT_REFRESH_LIFETIME", 60 * 24 * 7)),  # 7 days
    "ROTATE_REFRESH_TOKENS": env.bool(
        "JWT_ROTATE_REFRESH_TOKENS", False
    ),  # 是否在每次使用 refresh_token 之后刷新 refresh_token
    "BLACKLIST_AFTER_ROTATION": env.bool(
        "JWT_BLACKLIST_AFTER_ROTATION", False
    ),  # 是否在刷新 refresh_token 之后加入黑名单
    "UPDATE_LAST_LOGIN": env.bool("JWT_UPDATE_LAST_LOGIN", True),
}

# https://django-constance.readthedocs.io/en/latest/
# 动态配置库，可以把一部分配置放在数据库里面随时修改生效
CONSTANCE_BACKEND = "constance.backends.database.DatabaseBackend"

CONSTANCE_DATABASE_CACHE_BACKEND = "default" if CACHES.get("default", {}).get("LOCATION") else None
CONSTANCE_IGNORE_ADMIN_VERSION_CHECK = True
MODE_DEV = "dev"
MODE_DEMO = "demo"
MODE_PRODUCTION = "production"
DEFAULT_MODE = MODE_DEMO if env.bool("MODE_TEST", False) else MODE_PRODUCTION
CONSTANCE_ADDITIONAL_FIELDS = {
    "mode_select": [
        "django.forms.fields.ChoiceField",
        {
            "widget": "django.forms.Select",
            "choices": (
                ("dev", "开发模式，接口无需登录，任意用户名密码登录管理员用户admin"),
                (
                    "demo",
                    "演示模式，接口都需要登录，但随便输入用户名密码即可创建并登录普通用户，使用用户名admin即可登录管理员用户",
                ),
                ("production", "生产模式，接口都需要登录，需要正常输入用户名密码"),
            ),
        },
    ],
}
CONSTANCE_CONFIG = OrderedDict(
    [
        ("MODE", (DEFAULT_MODE, "系统运行的模式", "mode_select")),
    ]
)

EMBEDDING_API_URL = env.url("EMBEDDING_API_URL", "http://localhost:8000", schemes=["http", "https"]).geturl()
EMBEDDING_API_KEY = env.str("EMBEDDING_API_KEY", "")
EMBEDDING_MODEL = env.str("EMBEDDING_MODEL", "")
EMBEDDING_BATCH_SIZE = env.int("EMBEDDING_BATCH_SIZE", 100)

MIN_CHUNK_LENGTH = env.int("MIN_CHUNK_LENGTH", 500)
MAX_CHUNK_LENGTH = env.int("MAX_CHUNK_LENGTH", 700)

VECTOR_STORE_DIR = env.str("VECTOR_STORE_PATH", "vector_store")
VECTOR_STORE_PATH = BASE_DIR / VECTOR_STORE_DIR
if not VECTOR_STORE_PATH.exists():
    VECTOR_STORE_PATH.mkdir(parents=True, exist_ok=True)

KNOWLEDGE_DIR = BASE_DIR / "knowledge"  # ATA 等数据存储位置， 参考初始化脚本 src/scripts/init_ata_knowledge.py


OPENAI_API_URL = env.url("OPENAI_API_URL", "https://api.openai.com/v1", schemes=["http", "https"]).geturl()
OPENAI_API_KEY = env.str("OPENAI_API_KEY", "")
OPENAI_API_MODEL = env.str("OPENAI_API_MODEL", "")
# 给LLM模型提交的最大token数
OPENAI_MAX_TOKENS = env.int("OPENAI_MAX_TOKENS", 32000)
# 分割文本的结尾部分占用上下文的百分比
OPENAI_SPLIT_ENDING_PERCENTAGE = env.float("OPENAI_SPLIT_ENDING_PERCENTAGE", 0.2)


# 语言模型列表
LLMS = {
    "default": {
        "api_url": OPENAI_API_URL,
        "api_key": OPENAI_API_KEY,
        "api_model": OPENAI_API_MODEL,
        "max_tokens": OPENAI_MAX_TOKENS,
    },
    "glm4-chat": {
        "api_url": "http://**********:8300/v1",
        "api_key": "",
        "api_model": "glm4-chat",
        "max_tokens": 14000,
    },
}

CELERY_BROKER_URL = env.str("CELERY_BROKER_URL", REDIS_URL)
CELERY_RESULT_BACKEND = env.str("CELERY_RESULT_BACKEND", REDIS_URL)
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = "Asia/Shanghai"
CELERY_CONNECTION_MAX_RETRIES = None
CELERY_WORKER_CANCEL_LONG_RUNNING_TASKS_ON_CONNECTION_LOSS = False
CELERY_BEAT_SCHEDULE = {
    "check_and_run_tasks": {
        "task": "check_and_run_tasks",
        "schedule": 5.0,  # 每5秒运行一次
        "options": {"expires": 10},
    },
}

DEBOUNCE_TIMEOUT = env.int("DEBOUNCE_TIMEOUT", 30)


LOG_BASE_PATH = env.path("LOG_BASE", BASE_DIR / "logs")

Path.mkdir(LOG_BASE_PATH, parents=True, exist_ok=True)
# 动态控制日志文件数量，避免用户磁盘空间被日志文件占满
LOG_FILE_COUNT = env.int("LOG_FILE_COUNT", 1)

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        },
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
    },
    "formatters": {
        "django.server": {
            "()": "django.utils.log.ServerFormatter",
            "format": "[{server_time}] {message}",
            "style": "{",
        },
        "standard": {
            "format": "{asctime} [{processName}:{threadName}] [{filename}:{lineno}] [{levelname}]- {message}",
            "style": "{",
        },
        "normal": {
            "format": "[{asctime}] [{levelname}] {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
        },
        "django.server": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "django.server",
        },
        "mail_admins": {
            "level": "ERROR",
            "filters": ["require_debug_false"],
            "class": "django.utils.log.AdminEmailHandler",
        },
        "request_debug": {
            "level": "DEBUG",
            "class": "logging.handlers.RotatingFileHandler",  # 保存到文件，自动切
            "filename": LOG_BASE_PATH / "request_debug.log",
            # 日志文件
            "maxBytes": 1024 * 1024 * 50,  # 日单日志大小
            "backupCount": LOG_FILE_COUNT,  # 最多备份几个
            "formatter": "standard",
        },
        "normal": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",  # 保存到文件，自动切
            "filename": LOG_BASE_PATH / "normal.log",
            # 日志文件
            "maxBytes": 1024 * 1024 * 50,  # 单日志大小
            "backupCount": LOG_FILE_COUNT,  # 最多备份几个
            "formatter": "standard",
        },
        "error": {
            "level": "ERROR",
            "class": "logging.handlers.RotatingFileHandler",  # 保存到文件，自动切
            "filename": LOG_BASE_PATH / "error.log",
            # 日志文件
            "maxBytes": 1024 * 1024 * 50,  # 单日志大小
            "backupCount": LOG_FILE_COUNT,  # 最多备份几个
            "formatter": "standard",
        },
        "debug": {
            "level": "DEBUG",
            "class": "logging.handlers.RotatingFileHandler",  # 保存到文件，自动切
            "filename": LOG_BASE_PATH / "debug.log",
            # 日志文件
            "maxBytes": 1024 * 1024 * 50,  # 单日志大小
            "backupCount": LOG_FILE_COUNT,  # 最多备份几个
            "formatter": "standard",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "mail_admins", "normal", "error"],
            "level": "INFO",
        },
        "django.server": {
            "handlers": ["console", "django.server", "error"],
            "level": "INFO",
            "propagate": False,
        },
        "django.request": {
            "handlers": ["console", "request_debug", "error"],
            "level": "DEBUG",
            "propagate": False,
        },
        "normal": {
            "handlers": ["console", "normal", "error"],
            "level": "DEBUG",
            "propagate": False,
        },
        "root": {
            "handlers": ["console", "normal", "error"],
            "level": "DEBUG",
            "propagate": False,
        },
        "": {
            "handlers": ["console", "normal", "error"],
            "level": "DEBUG",
            "propagate": False,
        },
    },
}

CAPTCHA_ENABLED = env.bool("CAPTCHA_ENABLED", False)
CAPTCHA_FOREGROUND_COLOR = "#000000"
CAPTCHA_NOISE_FUNCTIONS = ("captcha.helpers.noise_dots",)
CAPTCHA_TIMEOUT = env.int("CAPTCHA_TIMEOUT", 5)
CAPTCHA_LENGTH = env.int("CAPTCHA_LENGTH", 4)
