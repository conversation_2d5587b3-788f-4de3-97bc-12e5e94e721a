DEBUG=false
# 连续下载内容时的时间间隔，单位为 秒。
SLEEP_TIME_INTERVAL=0.5

# 关系型数据库
OPENGAUSS_DRIVER=opengauss+psycopg2
OPENGAUSS_HOST=opengauss
OPENGAUSS_PORT=5432
OPENGAUSS_USER=gaussdb
OPENGAUSS_PWD=
OPENGAUSS_DB=naip_spider
OPENGAUSS_SCHEMA=spider

# KV数据库
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PWD=
REDIS_DATABASE=3


# 银河金控服务器上的MINIO 配置
MINIO_HOST=minio:9000
MINIO_BUCKET_NAME=spider
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=


KDL_SECRET_ID=
KDL_SECRET_KEY=
KDL_ORDER_ID=
KDL_ORDER_SECRET_ID=
KDL_ORDER_SECRET_KEY=
# 是否使用 TPS 隧道代理，如果是，则填写代理地址 KDL_TPS_PROXY，否则留空
KDL_USE_TPS=False
KDL_TPS_PROXY=
KDL_ORDER_USERNAME=
KDL_ORDER_PASSWORD=
KDL_ALARM_AMOUNT=15.0

JZL_KEY=
JZL_VERIFY=
JZL_ALARM_AMOUNT=10.0

SV_PASSWORD=
FLOWER_PASSWORD=

DEFAULT_CALLBACK_URL=

SMTP_SERVER=
SMTP_PORT=
SMTP_USER=
SMTP_PASSWORD=
SMTP_SENDER=

DINGTALK_WEBHOOK_URL=
DINGTALK_WEBHOOK_SECRET=

ADMIN_EMAILS=
