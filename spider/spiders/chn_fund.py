from typing import Any
from urllib.parse import urljoin

import scrapy
from scrapy.crawler import CrawlerProcess
from scrapy.http import Response
from scrapy.utils.project import get_project_settings

from spider.items.chn_fund import ChnFundArticleItem
from spider.spiders.base import BaseSpider


class ChnFundSpider(BaseSpider):
    """中国基金报爬虫."""

    name: str = "chn_fund"
    category: int = 6

    infos = []

    def get_aid(self, url):
        # 中国基金报的文章 url 诸如
        # https://www.chnfund.com/article/AR2955df72-41a3-bba4-f224-3a13ea4218b7
        # https://www.chnfund.com/article/AR20240720123446200
        # 取 最后一个下划线之后的部分作为 aid
        return url.split("/")[-1]

    def get_article_url(self, detail_url):
        # 中国基金报的文章 url
        return urljoin("https://www.chnfund.com/", detail_url)

    def start_requests(self):
        if self.infos:
            for info in self.infos:
                yield scrapy.Request(
                    url=self.get_article_url(info["detailUrl"]),
                    callback=self.parse,
                    meta={"item": ChnFundArticleItem()},
                )

    def parse(self, response: Response, **kwargs: Any):
        if body := response.xpath("//div[contains(@class, 'content-body')]"):
            ...
        else:
            self.logger.error(f"未找到正文内容: {response.url}")
            return

        item = response.meta["item"]
        content = body.css(".article-content")
        if not content:  # 非正文页
            return

        item["detail_path"] = ""
        item["aid"] = self.get_aid(response.url)
        item["url"] = response.url
        item["body"] = body.extract_first()
        if title := body.css(".article-title::text"):
            item["title"] = title.get("").strip()
        elif title := response.xpath("//title/text()"):
            item["title"] = title.get("").strip()
        author = response.xpath("//meta[@name='author']/@content")
        if author:
            item["author"] = author.get("").strip()
        create_time = body.css(".publish-time::text")
        if create_time:
            item["create_time"] = create_time.get("").strip()
            item["update_time"] = item["create_time"]  # 中国基金报没有更新时间

        item["content"] = self.recursive_extract_rich_text(content[0], response.url)
        image_urls = content.css("img::attr(src)").extract()
        absolute_image_urls = []
        for image_url in image_urls:
            # 将相对 URL 转换为绝对 URL
            absolute_image_url = urljoin(response.url, image_url)
            absolute_image_urls.append(absolute_image_url)

        item["image_urls"] = absolute_image_urls

        item["info"] = {}  # 暂时无有用数据
        item["links"] = self.extract_links(content.xpath(".//a"), item["url"])

        yield item


if __name__ == "__main__":
    settings = get_project_settings()
    settings.setdict(
        {
            "RETRY_ENABLED": True,
            "RETRY_TIMES": 3,
        }
    )

    start_urls = ["https://www.chnfund.com/fund?pageIndex=1"]
    p = CrawlerProcess(settings)
    p.crawl(ChnFundSpider, start_urls=start_urls, task_id="test_task_id", target_id=20)
    p.start()
