import re
from typing import Any
from urllib.parse import urljoin

import scrapy
from scrapy.crawler import CrawlerProcess
from scrapy.http import Response
from scrapy.utils.project import get_project_settings

from spider.items.nbs import NbsArticleItem
from spider.spiders.base import BaseSpider
from src.dao.open_gauss.spider_results import ResultDao
from src.storage.open_gauss.connection import get_session


class NbsSpider(BaseSpider):
    """国家统计局爬虫."""

    name: str = "nbs"
    category: int = 5

    regex = re.compile(r"(\d+).html?")

    def get_aid(self, url):
        # 财政部的文章 url 都是诸如 https://www.gov.cn/yaowen/liebiao/202406/content_6959480.htm
        # 取 htm之前，下划线之后的部分作为 aid
        try:
            return self.regex.findall(url)[-1]
        except IndexError:
            self.logger.error(f"未找到文章 aid: {url}")
            return ""

    def start_requests(self):
        if self.start_urls:
            for url in self.start_urls:
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_page,
                )

    def parse_page(self, response: Response, **kwargs: Any):
        body = response.css(".list-content")
        if not body:
            self.logger.error(f"未找到列表页内容: {response.url}")
            return
        pages = body.css(".list-content a::attr(href)").extract()
        page_aids = [self.get_aid(page) for page in pages]
        with get_session() as db:
            non_exist_aids = ResultDao.filter_exist_aids(db, self.target_id, page_aids)
        pages_to_crawl = [page for page in pages if self.get_aid(page) in non_exist_aids]
        for page in pages_to_crawl:
            yield response.follow(
                url=page,
                callback=self.parse_detail,
                meta={"item": NbsArticleItem()},
            )

    def parse_detail(self, response: Response, **kwargs: Any):

        if body := response.xpath("//div[contains(@class, 'content')]"):
            ...
        elif body := response.xpath("//div[contains(@class, 'sj_container')]"):
            ...
        elif body := response.xpath("//div[contains(@class, 'TRS_UEDITOR')]"):
            ...
        else:
            self.logger.error(f"未找到正文内容: {response.url}")
            return

        item = response.meta["item"]
        if content := body.css(".pages_content"):
            ...
        elif content := body.css(".TRS_UEDITOR"):
            ...
        elif content := body.css(".trs_editor_view"):
            ...
        item["detail_path"] = ""
        item["aid"] = self.get_aid(response.url)
        item["url"] = response.url
        item["body"] = body.extract_first()
        title = response.xpath("//title/text()")
        if title:
            item["title"] = title.get("").strip()
        author = response.xpath("//meta[@name='author']/@content")
        if author:
            item["author"] = author.get("").strip()
        create_time = response.xpath("//meta[@name='firstpublishedtime']/@content")
        if create_time:
            item["create_time"] = create_time.get("").strip()
        update_time = response.xpath("//meta[@name='lastmodifiedtime']/@content")
        if update_time:
            item["update_time"] = update_time.get("").strip()

        item["content"] = self.recursive_extract_rich_text(content[0], response.url)
        image_urls = content.css("img::attr(src)").extract()
        absolute_image_urls = []
        for image_url in image_urls:
            # 将相对 URL 转换为绝对 URL
            absolute_image_url = urljoin(response.url, image_url)
            absolute_image_urls.append(absolute_image_url)

        item["image_urls"] = absolute_image_urls

        item["info"] = {}  # 暂时无有用数据
        item["links"] = self.extract_links(content.xpath(".//a"), item["url"])

        yield item


if __name__ == "__main__":
    settings = get_project_settings()
    settings.setdict(
        {
            "RETRY_ENABLED": True,
            "RETRY_TIMES": 3,
        }
    )

    start_urls = ["https://www.stats.gov.cn/xw/szyw/gwyxx/index.html"]
    p = CrawlerProcess(settings)
    p.crawl(NbsSpider, start_urls=start_urls, task_id="test_task_id", target_id=19)
    p.start()
