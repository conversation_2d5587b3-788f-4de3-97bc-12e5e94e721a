import scrapy

from spider.items.base import BaseItem
from src.utils.json_encoder import normalize_time


class NbsArticleItem(BaseItem):
    """国家统计局国务院信息文章."""

    title = scrapy.Field()
    content = scrapy.Field()
    images = scrapy.Field()
    image_urls = scrapy.Field()
    links = scrapy.Field()

    author = scrapy.Field()

    aid = scrapy.Field()

    create_time = scrapy.Field()
    update_time = scrapy.Field()

    url = scrapy.Field()

    def to_result(self, spider):
        return {
            "target_id": spider.target_id,
            "target_category": spider.category,
            "source_aid": self["aid"],
            "source_appmsgid": int(self["aid"]) if str(self["aid"]).isdecimal() else 0,
            "source_author_name": self["author"],
            "source_title": self["title"],
            "source_link": self["url"],
            "source_link_detail_path": self["detail_path"],
            "source_create_time": normalize_time(self["create_time"]),
            "source_update_time": normalize_time(self["create_time"]),
            "source_info": self["info"],
            "task_id": spider.task_id,
            "content": self["content"],
            "images": self["images"],
            "links": self["links"],
            **super().to_result(spider),
        }

    def get_page_file_name(self):
        return f"nbs/{self['aid']}.html"

    def get_image_file_name(self, image_guid):
        return f"nbs_images/{self['aid']}/{image_guid}.jpg"

    def get_thumb_file_name(self, image_guid):
        return f"nbs_thumbs/{self['aid']}/{image_guid}.jpg"
