import logging

from conf.settings import MINIO_OPTIONS
from src.dao.minio.minio import MinioDao

logger = logging.getLogger(__name__)


class MinioFilesStore:

    def __init__(self, uri, **kwargs):
        options = MINIO_OPTIONS
        if kwargs:
            options.update(kwargs)
        self.minio = MinioDao(**options)

    def stat_file(self, path, info):
        # TODO 从 minio 获取文件信息
        return {"last_modified": None, "checksum": None}

    def persist_file(self, path, buf, info, meta=None, headers=None):
        self.minio.put_raw_object(object_name=path, data=buf)
