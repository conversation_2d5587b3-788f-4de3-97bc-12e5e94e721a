model: command-nightly
label:
  zh_<PERSON>: command-nightly
  en_US: command-nightly
model_type: llm
features:
  - agent-thought
model_properties:
  mode: completion
  context_size: 4096
parameter_rules:
  - name: temperature
    use_template: temperature
    max: 5.0
  - name: p
    use_template: top_p
    default: 0.75
    min: 0.01
    max: 0.99
  - name: k
    label:
      zh_<PERSON>: 取样数量
      en_US: Top k
    type: int
    help:
      zh_<PERSON>: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
    default: 0
    min: 0
    max: 500
  - name: presence_penalty
    use_template: presence_penalty
  - name: frequency_penalty
    use_template: frequency_penalty
  - name: max_tokens
    use_template: max_tokens
    default: 1024
    max: 4096
pricing:
  input: '1.0'
  output: '2.0'
  unit: '0.000001'
  currency: USD
