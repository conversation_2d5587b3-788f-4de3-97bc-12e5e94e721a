model: mistralai/mistral-7b-instruct
label:
  en_US: mistral-7b-instruct
model_type: llm
features:
  - agent-thought
model_properties:
  mode: completion
  context_size: 8000
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 0.7
    min: 0
    max: 1
  - name: top_p
    use_template: top_p
    default: 1
    min: 0
    max: 1
  - name: top_k
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
  - name: max_tokens
    use_template: max_tokens
    default: 1024
    min: 1
    max: 2048
pricing:
  input: "0.07"
  output: "0.07"
  unit: "0.000001"
  currency: USD
