model: meta-llama/llama-3.1-8b-instruct
label:
  en_US: llama-3.1-8b-instruct
model_type: llm
model_properties:
  mode: chat
  context_size: 131072
parameter_rules:
  - name: temperature
    use_template: temperature
  - name: top_p
    use_template: top_p
  - name: top_k
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
  - name: max_tokens
    use_template: max_tokens
    required: true
    default: 512
    min: 1
    max: 131072
pricing:
  input: "0.06"
  output: "0.06"
  unit: "0.000001"
  currency: USD
