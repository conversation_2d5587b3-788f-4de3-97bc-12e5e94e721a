model: meta.llama-3-70b-instruct
label:
  zh_Hans: meta.llama-3-70b-instruct
  en_US: meta.llama-3-70b-instruct
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 131072
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 1
    max: 2.0
  - name: topP
    use_template: top_p
    default: 0.75
    min: 0
    max: 1
  - name: topK
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
    default: 0
    min: 0
    max: 500
  - name: presencePenalty
    use_template: presence_penalty
    min: -2
    max: 2
    default: 0
  - name: frequencyPenalty
    use_template: frequency_penalty
    min: -2
    max: 2
    default: 0
  - name: maxTokens
    use_template: max_tokens
    default: 600
    max: 8000
pricing:
  input: '0.015'
  output: '0.015'
  unit: '0.0001'
  currency: USD
deprecated: true
