background: "#ffecff"
configurate_methods:
  - predefined-model
  - customizable-model
extra:
  python:
    model_sources:
      - models/llm/llm.py
      - models/rerank/rerank.py
      - models/text_embedding/text_embedding.py
      - models/tts/tts.py
      - models/speech2text/speech2text.py
    provider_source: provider/siliconflow.py
help:
  title:
    en_US: Get your API Key from SiliconFlow
    zh_Hans: 从 SiliconFlow 获取 API Key
  url:
    en_US: https://cloud.siliconflow.cn/account/ak
icon_large:
  en_US: siliconflow.svg
icon_small:
  en_US: siliconflow_square.svg
label:
  en_US: SiliconFlow
  zh_Hans: 硅基流动
model_credential_schema:
  credential_form_schemas:
    - label:
        en_US: API Key
      placeholder:
        en_US: Enter your API Key
        zh_Hans: 在此输入您的 API Key
      required: true
      type: secret-input
      variable: api_key
    - default: "4096"
      label:
        en_US: Model context size
        zh_Hans: 模型上下文长度
      placeholder:
        en_US: Enter your Model context size
        zh_Hans: 在此输入您的模型上下文长度
      required: true
      type: text-input
      variable: context_size
    - default: "4096"
      label:
        en_US: Upper bound for max tokens
        zh_Hans: 最大 token 上限
      show_on:
        - value: llm
          variable: __model_type
      type: text-input
      variable: max_tokens
    - default: no_call
      label:
        en_US: Function calling
      options:
        - label:
            en_US: Not Support
            zh_Hans: 不支持
          value: no_call
        - label:
            en_US: Support
            zh_Hans: 支持
          value: function_call
      required: false
      show_on:
        - value: llm
          variable: __model_type
      type: select
      variable: function_calling_type
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
models:
  llm:
    position: models/llm/_position.yaml
    predefined:
      - models/llm/*.yaml
  rerank:
    predefined:
      - models/rerank/*.yaml
  speech2text:
    predefined:
      - models/speech2text/*.yaml
  text_embedding:
    predefined:
      - models/text_embedding/*.yaml
  tts:
    predefined:
      - models/tts/*.yaml
provider: siliconflow
provider_credential_schema:
  credential_form_schemas:
    - label:
        en_US: API Key
      placeholder:
        en_US: Enter your API Key
        zh_Hans: 在此输入您的 API Key
      required: true
      type: secret-input
      variable: api_key
supported_model_types:
  - llm
  - text-embedding
  - rerank
  - speech2text
  - tts
