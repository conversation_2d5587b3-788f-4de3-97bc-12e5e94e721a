model: baichuan4-turbo
label:
  en_US: Baichuan4-Turbo
model_type: llm
features:
  - agent-thought
  - multi-tool-call
model_properties:
  mode: chat
  context_size: 32000
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 0.3
  - name: top_p
    use_template: top_p
    default: 0.85
  - name: top_k
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    min: 0
    max: 20
    default: 5
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
  - name: max_tokens
    use_template: max_tokens
    default: 2048
  - name: res_format
    label:
      zh_Hans: 回复格式
      en_US: Response Format
    type: string
    help:
      zh_Hans: 指定模型必须输出的格式
      en_US: specifying the format that the model must output
    required: false
    options:
      - text
      - json_object
  - name: with_search_enhance
    label:
      zh_Hans: 搜索增强
      en_US: Search Enhance
    type: boolean
    help:
      zh_Hans: 允许模型自行进行外部搜索，以增强生成结果。
      en_US: Allow the model to perform external search to enhance the generation results.
    required: false
