## Overview

xAI is developing a family of Large Language Models (LLMs) called **Grok**. Grok models use next-token prediction to perform tasks, with a focus on providing helpful and insightful responses. Key models include `grok-2-vision-1212` and `grok-2-1212`. Depending on the model specifications, users can interact with Grok models to find answers, interpret images, and generate images. xAI offers an API interface for developers to programmatically interact with Grok models.

## Configuration

After installing the plugin, configure the API key and API base within the Model Provider settings. Obtain your API key from [here](https://x.ai/api). Once saved, you can begin using xAI to build your AI agents and agentic workflows.

![](./_assets/x_ai_config.PNG)