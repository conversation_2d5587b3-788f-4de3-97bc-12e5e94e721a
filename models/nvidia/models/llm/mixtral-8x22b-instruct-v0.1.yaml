model: mistralai/mixtral-8x22b-instruct-v0.1
label:
  zh_Hans: mistral<PERSON>/mixtral-8x22b-instruct-v0.1
  en_US: mistralai/mixtral-8x22b-instruct-v0.1
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 64000
parameter_rules:
  - name: temperature
    use_template: temperature
    min: 0
    max: 1
    default: 0.5
  - name: top_p
    use_template: top_p
    min: 0
    max: 1
    default: 1
  - name: max_tokens
    use_template: max_tokens
    min: 1
    max: 1024
    default: 1024
  - name: frequency_penalty
    use_template: frequency_penalty
    min: -2
    max: 2
    default: 0
  - name: presence_penalty
    use_template: presence_penalty
    min: -2
    max: 2
    default: 0
