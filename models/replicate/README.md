# Overview
Replicate is a cloud-based platform designed to simplify the deployment and scaling of machine learning models. It provides users with access to a vast library of open-source AI models, enabling developers to run and fine-tune these models with minimal coding effort. The platform aims to democratize machine learning by making it accessible to a broader audience, including software engineers, data scientists, and product managers.

Dify supports accessing [Language models](https://replicate.com/collections/language-models) and [Embedding models](https://replicate.com/collections/embedding-models) on Replicate. Language models correspond to <PERSON><PERSON>'s reasoning model, and Embedding models correspond to <PERSON><PERSON>'s Embedding model.

# Configure
1. You need to have a [Replicate account](https://replicate.com/signin?next=/docs).
2. [Get API Key](https://replicate.com/signin?next=/docs).
3. Pick a model. Select the model under [Language models](https://replicate.com/collections/language-models) and [Embedding models](https://replicate.com/collections/embedding-models).
4. Add models in Dify's Settings -> Model Provider -> Replicate.

![](_assets/create_replicate_model.png)

The API key is the API Key set in step 2. Model Name and Model Version can be found on the model details page:

![](_assets/api_key.png)