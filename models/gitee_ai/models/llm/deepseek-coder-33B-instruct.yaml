model: deepseek-coder-33B-instruct
label:
  zh_Hans: deepseek-coder-33B-instruct
  en_US: deepseek-coder-33B-instruct
model_type: llm
features:
  - tool-call
model_properties:
  mode: chat
  context_size: 8192
parameter_rules:
  - name: stream
    label:
      en_US: "Stream"
      zh_Hans: "流式"
    type: boolean
    default: true
    required: true
    help:
      en_US: "Whether to return the results in batches through streaming. If set to true, the generated text will be pushed to the user in real time during the generation process."
      zh_<PERSON>: "是否通过流式分批返回结果。如果设置为 true，生成过程中实时地向用户推送每一部分生成的文本。"

  - name: max_tokens
    use_template: max_tokens
    label:
      en_US: "<PERSON> Tokens"
      zh_Hans: "最大Token数"
    type: int
    default: 512
    min: 1
    max: 8192
    required: true
    help:
      en_US: "The maximum number of tokens that can be generated by the model varies depending on the model."
      zh_<PERSON>: "模型可生成的最大 token 个数，不同模型上限不同。"

  - name: temperature
    use_template: temperature
    label:
      en_US: "Temperature"
      zh_Hans: "采样温度"
    type: float
    default: 0.7
    min: 0.0
    max: 1.0
    required: true
    help:
      en_US: "The randomness of the sampling temperature control output. The temperature value is within the range of [0.0, 1.0]. The higher the value, the more random and creative the output; the lower the value, the more stable it is. It is recommended to adjust either top_p or temperature parameters according to your needs to avoid adjusting both at the same time."
      zh_Hans: "采样温度控制输出的随机性。温度值在 [0.0, 1.0] 范围内，值越高，输出越随机和创造性；值越低，输出越稳定。建议根据需求调整 top_p 或 temperature 参数，避免同时调整两者。"

  - name: top_p
    use_template: top_p
    label:
      en_US: "Top P"
      zh_Hans: "Top P"
    type: float
    default: 0.7
    min: 0.0
    max: 1.0
    precision: 1
    required: true
    help:
      en_US: "The value range of the sampling method is [0.0, 1.0]. The top_p value determines that the model selects tokens from the top p% of candidate words with the highest probability; when top_p is 0, this parameter is invalid. It is recommended to adjust either top_p or temperature parameters according to your needs to avoid adjusting both at the same time."
      zh_Hans: "采样方法的取值范围为 [0.0,1.0]。top_p 值确定模型从概率最高的前p%的候选词中选取 tokens；当 top_p 为 0 时，此参数无效。建议根据需求调整 top_p 或 temperature 参数，避免同时调整两者。"

  - name: top_k
    label:
      en_US: "Top K"
      zh_Hans: "Top K"
    type: int
    default: 50
    min: 0
    max: 100
    required: true
    help:
      en_US: "The value range is [0,100], which limits the model to only select from the top k words with the highest probability when choosing the next word at each step. The larger the value, the more diverse text generation will be."
      zh_Hans: "取值范围为 [0,100]，限制模型在每一步选择下一个词时，只从概率最高的前 k 个词中选取。数值越大，文本生成越多样。"

  - name: frequency_penalty
    use_template: frequency_penalty
    label:
      en_US: "Frequency Penalty"
      zh_Hans: "频率惩罚"
    type: float
    default: 0
    min: -1.0
    max: 1.0
    precision: 1
    required: false
    help:
      en_US: "Used to adjust the frequency of repeated content in automatically generated text. Positive numbers reduce repetition, while negative numbers increase repetition. After setting this parameter, if a word has already appeared in the text, the model will decrease the probability of choosing that word for subsequent generation."
      zh_Hans: "用于调整自动生成文本中重复内容的频率。正数减少重复，负数增加重复。设置此参数后，如果一个词在文本中已经出现过，模型在后续生成中选择该词的概率会降低。"

  - name: user
    label:
      en_US: "User"
      zh_Hans: "用户"
    type: string
    required: false
    help:
      en_US: "Used to track and differentiate conversation requests from different users."
      zh_Hans: "用于追踪和区分不同用户的对话请求。"
