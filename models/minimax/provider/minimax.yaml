background: '#FFEFEF'
configurate_methods:
- predefined-model
extra:
  python:
    model_sources:
    - models/llm/llm.py
    - models/text_embedding/text_embedding.py
    - models/tts/tts.py
    provider_source: provider/minimax.py
help:
  title:
    en_US: Get your API Key from Minimax
    zh_Hans: 从 Minimax 获取您的 API Key
  url:
    en_US: https://api.minimax.chat/user-center/basic-information/interface-key
icon_large:
  en_US: icon_l_en.png
icon_small:
  en_US: icon_s_en.png
label:
  en_US: Minimax
models:
  llm:
    predefined:
    - models/llm/*.yaml
  text_embedding:
    predefined:
    - models/text_embedding/*.yaml
  tts:
    predefined:
    - models/tts/*.yaml
provider: minimax
provider_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Key
    placeholder:
      en_US: Enter your API Key
      zh_Hans: 在此输入您的 API Key
    required: true
    type: secret-input
    variable: minimax_api_key
  - label:
      en_US: Group ID
    placeholder:
      en_US: Enter your group ID
      zh_Hans: 在此输入您的 Group ID
    required: true
    type: text-input
    variable: minimax_group_id
supported_model_types:
- llm
- text-embedding
- tts
