model: abab6-chat
label:
  en_US: Abab6-Chat
model_type: llm
features:
  - agent-thought
  - tool-call
  - stream-tool-call
model_properties:
  mode: chat
  context_size: 32768
parameter_rules:
  - name: temperature
    use_template: temperature
    min: 0.01
    max: 1
    default: 0.1
  - name: top_p
    use_template: top_p
    min: 0.01
    max: 1
    default: 0.9
  - name: max_tokens
    use_template: max_tokens
    required: true
    default: 2048
    min: 1
    max: 32768
  - name: mask_sensitive_info
    type: boolean
    default: true
    label:
      zh_Hans: 隐私保护
      en_US: Moderate
    help:
      zh_Hans: 对输出中易涉及隐私问题的文本信息进行打码，目前包括但不限于邮箱、域名、链接、证件号、家庭住址等，默认true，即开启打码
      en_US: Mask the sensitive info of the generated content, such as email/domain/link/address/phone/id..
  - name: presence_penalty
    use_template: presence_penalty
  - name: frequency_penalty
    use_template: frequency_penalty
pricing:
  input: '0.1'
  output: '0.1'
  unit: '0.001'
  currency: RMB
