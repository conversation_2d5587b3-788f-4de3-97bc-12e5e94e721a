<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TransMediaX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">TransMediaX</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.html">Admin</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>Media Conversion</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Input</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="inputType" class="form-label">Input Type</label>
                            <select class="form-select" id="inputType">
                                <option value="text">Text</option>
                                <option value="image">Image</option>
                                <option value="audio">Audio</option>
                                <option value="video">Video</option>
                                <option value="model3d">3D Model</option>
                            </select>
                        </div>
                        
                        <div id="textInput" class="input-section">
                            <div class="mb-3">
                                <label for="textContent" class="form-label">Text Content</label>
                                <textarea class="form-control" id="textContent" rows="5"></textarea>
                            </div>
                        </div>
                        
                        <div id="fileInput" class="input-section d-none">
                            <div class="mb-3">
                                <label for="fileUpload" class="form-label">Upload File</label>
                                <input class="form-control" type="file" id="fileUpload">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Output</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="outputType" class="form-label">Output Type</label>
                            <select class="form-select" id="outputType">
                                <option value="text">Text</option>
                                <option value="image">Image</option>
                                <option value="audio">Audio</option>
                                <option value="video">Video</option>
                                <option value="model3d">3D Model</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="parameters" class="form-label">Parameters (JSON)</label>
                            <textarea class="form-control" id="parameters" rows="5">{"positive_prompt": "", "negative_prompt": ""}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12 text-center">
                <button id="convertBtn" class="btn btn-primary">Convert</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Conversion Progress</h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="statusMessage" class="text-center">Ready to convert</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Results</h5>
                    </div>
                    <div class="card-body" id="resultsContainer">
                        <p class="text-center text-muted">No results yet</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>History</h5>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="historyTable">
                                <!-- History items will be added here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
