/**
 * API client for TransMediaX
 */
class TransMediaXAPI {
    constructor(baseUrl = '/api/v1') {
        this.baseUrl = baseUrl;
    }

    /**
     * Make an API request
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {object} data - Request data
     * @param {object} headers - Request headers
     * @returns {Promise<object>} - Response data
     */
    async request(endpoint, method = 'GET', data = null, headers = {}) {
        let url = `${this.baseUrl}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (data) {
            if (method === 'GET') {
                const params = new URLSearchParams(data);
                url += `?${params.toString()}`;
            } else {
                options.body = JSON.stringify(data);
            }
        }

        try {
            const response = await fetch(url, options);

            if (response.status === 204) {
                return null; // No content
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }

            return await response.text();
        } catch (error) {
            console.error('API request error:', error);
            throw error;
        }
    }

    /**
     * Upload a file
     * @param {File} file - File to upload
     * @param {string} mediaType - Media type
     * @param {string} contentType - Content type
     * @param {object} metadata - Metadata
     * @param {string} userId - User ID
     * @returns {Promise<object>} - Response data
     */
    async uploadFile(file, mediaType, contentType = '', metadata = {}, userId = null) {
        const url = `${this.baseUrl}/media/upload/`;
        const formData = new FormData();
        formData.append('file', file);
        formData.append('media_type', mediaType);
        formData.append('content_type', contentType);
        formData.append('metadatas', JSON.stringify(metadata));

        if (userId) {
            formData.append('user_id', userId);
        }

        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });

            return await response.json();
        } catch (error) {
            console.error('File upload error:', error);
            throw error;
        }
    }

    /**
     * Create text media
     * @param {string} textContent - Text content
     * @param {string} userId - User ID
     * @param {object} metadata - Metadata
     * @returns {Promise<object>} - Response data
     */
    async createTextMedia(textContent, userId = null, metadata = {}) {
        return this.request('/media/text/', 'POST', {
            text_content: textContent,
            user_id: userId,
            metadatas: metadata
        });
    }

    /**
     * Create a conversion task
     * @param {string} taskType - Task type (e.g., text_to_image)
     * @param {Array<string>} inputMediaIds - Input media IDs
     * @param {object} parameters - Task parameters
     * @param {string} userId - User ID
     * @returns {Promise<object>} - Response data
     */
    async createTask(taskType, inputMediaIds, parameters = {}, userId = null) {
        return this.request('/tasks/', 'POST', {
            task_type: taskType,
            input_media_ids: inputMediaIds,
            parameters,
            user_id: userId
        });
    }

    /**
     * Get task progress
     * @param {string} taskId - Task ID
     * @returns {Promise<object>} - Response data
     */
    async getTaskProgress(taskId) {
        return this.request(`/tasks/${taskId}/progress/`);
    }

    /**
     * Get task details
     * @param {string} taskId - Task ID
     * @returns {Promise<object>} - Response data
     */
    async getTask(taskId) {
        return this.request(`/tasks/${taskId}/`);
    }

    /**
     * List tasks
     * @param {string} userId - User ID
     * @param {string} status - Task status
     * @param {number} limit - Limit
     * @param {number} offset - Offset
     * @returns {Promise<Array<object>>} - Response data
     */
    async listTasks(userId = null, status = null, limit = 100, offset = 0) {
        const params = {};
        if (userId) params.user_id = userId;
        if (status) params.status = status;
        params.limit = limit;
        params.offset = offset;

        return this.request('/tasks/', 'GET', params);
    }

    /**
     * Get media preview
     * @param {string} mediaId - Media ID
     * @returns {Promise<object>} - Response data
     */
    async getMediaPreview(mediaId) {
        return this.request(`/media/${mediaId}/preview/`);
    }

    /**
     * Get admin configuration
     * @param {string} username - Admin username
     * @param {string} password - Admin password
     * @returns {Promise<object>} - Response data
     */
    async getAdminConfig(username, password) {
        const headers = {
            'Authorization': `Basic ${btoa(`${username}:${password}`)}`
        };

        return this.request('/admin/config/', 'GET', null, headers);
    }

    /**
     * Update admin configuration
     * @param {object} config - Configuration
     * @param {string} username - Admin username
     * @param {string} password - Admin password
     * @returns {Promise<object>} - Response data
     */
    async updateAdminConfig(config, username, password) {
        const headers = {
            'Authorization': `Basic ${btoa(`${username}:${password}`)}`
        };

        return this.request('/admin/config/', 'POST', config, headers);
    }

    /**
     * List converters
     * @param {string} username - Admin username
     * @param {string} password - Admin password
     * @returns {Promise<Array<object>>} - Response data
     */
    async listConverters(username, password) {
        const headers = {
            'Authorization': `Basic ${btoa(`${username}:${password}`)}`
        };

        return this.request('/admin/converters/', 'GET', null, headers);
    }
}
