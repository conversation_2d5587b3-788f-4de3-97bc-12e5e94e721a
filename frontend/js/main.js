/**
 * Main JavaScript for TransMediaX frontend
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize API client
    const api = new TransMediaXAPI();

    // DOM elements
    const inputTypeSelect = document.getElementById('inputType');
    const outputTypeSelect = document.getElementById('outputType');
    const textInputSection = document.getElementById('textInput');
    const fileInputSection = document.getElementById('fileInput');
    const textContentTextarea = document.getElementById('textContent');
    const fileUploadInput = document.getElementById('fileUpload');
    const filePreviewContainer = document.getElementById('filePreviewContainer');
    const parametersTextarea = document.getElementById('parameters');
    const convertBtn = document.getElementById('convertBtn');
    const progressBar = document.getElementById('progressBar');
    const statusMessage = document.getElementById('statusMessage');
    const resultsContainer = document.getElementById('resultsContainer');
    const historyTable = document.getElementById('historyTable');

    // Current task ID
    let currentTaskId = null;
    let progressInterval = null;

    // User ID (for simplicity, we'll generate a random one and store in localStorage)
    let userId = localStorage.getItem('userId');
    if (!userId) {
        userId = 'user_' + Math.random().toString(36).substring(2, 15);
        localStorage.setItem('userId', userId);
    }

    // Load task history from localStorage
    const taskHistory = JSON.parse(localStorage.getItem('taskHistory') || '[]');

    // Event listeners
    inputTypeSelect.addEventListener('change', updateInputSection);
    fileUploadInput.addEventListener('change', previewFile);
    convertBtn.addEventListener('click', startConversion);

    // Initialize
    updateInputSection();
    loadTaskHistory();

    /**
     * Update input section based on selected input type
     */
    function updateInputSection() {
        const inputType = inputTypeSelect.value;

        if (inputType === 'text') {
            textInputSection.classList.remove('d-none');
            fileInputSection.classList.add('d-none');
        } else {
            textInputSection.classList.add('d-none');
            fileInputSection.classList.remove('d-none');
        }

        // 清除文件预览
        filePreviewContainer.classList.add('d-none');
        filePreviewContainer.innerHTML = '';
        fileUploadInput.value = '';
    }

    /**
     * 预览用户选择的文件
     */
    function previewFile() {
        const file = fileUploadInput.files[0];
        if (!file) {
            filePreviewContainer.classList.add('d-none');
            filePreviewContainer.innerHTML = '';
            return;
        }

        const inputType = inputTypeSelect.value;
        filePreviewContainer.innerHTML = '';

        // 根据文件类型创建预览
        if (inputType === 'image' && file.type.startsWith('image/')) {
            const img = document.createElement('img');
            img.classList.add('img-fluid', 'mb-2');
            img.file = file;
            filePreviewContainer.appendChild(img);

            const reader = new FileReader();
            reader.onload = (e) => { img.src = e.target.result; };
            reader.readAsDataURL(file);

            filePreviewContainer.classList.remove('d-none');
        }
        else if (inputType === 'audio' && file.type.startsWith('audio/')) {
            const audio = document.createElement('audio');
            audio.controls = true;
            audio.classList.add('w-100', 'mb-2');
            filePreviewContainer.appendChild(audio);

            const reader = new FileReader();
            reader.onload = (e) => { audio.src = e.target.result; };
            reader.readAsDataURL(file);

            filePreviewContainer.classList.remove('d-none');
        }
        else if (inputType === 'video' && file.type.startsWith('video/')) {
            const video = document.createElement('video');
            video.controls = true;
            video.classList.add('w-100', 'mb-2');
            filePreviewContainer.appendChild(video);

            const reader = new FileReader();
            reader.onload = (e) => { video.src = e.target.result; };
            reader.readAsDataURL(file);

            filePreviewContainer.classList.remove('d-none');
        }
        else {
            // 其他类型文件显示文件信息
            const fileInfo = document.createElement('div');
            fileInfo.classList.add('alert', 'alert-info', 'mb-0');
            fileInfo.innerHTML = `
                <strong>文件信息:</strong><br>
                名称: ${file.name}<br>
                类型: ${file.type || '未知'}<br>
                大小: ${(file.size / 1024).toFixed(2)} KB
            `;
            filePreviewContainer.appendChild(fileInfo);
            filePreviewContainer.classList.remove('d-none');
        }
    }

    /**
     * Start conversion process
     */
    async function startConversion() {
        try {
            // Reset UI
            progressBar.style.width = '0%';
            statusMessage.textContent = 'Starting conversion...';
            resultsContainer.innerHTML = '<p class="text-center text-muted">Processing...</p>';

            // Get input type and output type
            const inputType = inputTypeSelect.value;
            const outputType = outputTypeSelect.value;

            // Validate input
            if (inputType === outputType) {
                statusMessage.textContent = 'Input and output types cannot be the same';
                return;
            }

            // Create task type
            const taskType = `${inputType}_to_${outputType}`;

            // Get parameters
            let parameters = {};
            try {
                // Only parse parameters if the textarea is not empty
                if (parametersTextarea.value.trim()) {
                    parameters = JSON.parse(parametersTextarea.value);
                }
            } catch (error) {
                statusMessage.textContent = 'Invalid parameters JSON';
                return;
            }

            // Create input media
            let inputMediaId;

            if (inputType === 'text') {
                // Create text media
                const textContent = textContentTextarea.value.trim();
                if (!textContent) {
                    statusMessage.textContent = 'Text content cannot be empty';
                    return;
                }

                const textMedia = await api.createTextMedia(textContent, userId);
                inputMediaId = textMedia.id;
            } else {
                // Upload file
                const file = fileUploadInput.files[0];
                if (!file) {
                    statusMessage.textContent = 'Please select a file';
                    return;
                }

                const fileMedia = await api.uploadFile(file, inputType, '', {}, userId);
                inputMediaId = fileMedia.id;
            }

            // Create conversion task
            const task = await api.createTask(taskType, [inputMediaId], parameters, userId);
            currentTaskId = task.id;

            // Add to task history
            addTaskToHistory(task);

            // Start progress tracking
            startProgressTracking(task.id);
        } catch (error) {
            console.error('Conversion error:', error);
            statusMessage.textContent = `Error: ${error.message}`;
        }
    }

    /**
     * Start tracking task progress
     * @param {string} taskId - Task ID
     */
    function startProgressTracking(taskId) {
        // Clear any existing interval
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // Update UI to show processing state
        progressBar.style.width = '0%';
        progressBar.textContent = '';
        statusMessage.textContent = '处理中...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">正在处理您的请求，请稍候...</p></div>';

        // Start new interval - just check status, don't show fake progress
        progressInterval = setInterval(async () => {
            try {
                const progress = await api.getTaskProgress(taskId);

                // Update status message with more details
                let statusText = '';
                switch(progress.status) {
                    case 'pending':
                        statusText = '等待处理中...';
                        break;
                    case 'processing':
                        statusText = progress.current_step ? `${progress.current_step}` : '正在处理...';
                        break;
                    case 'completed':
                        statusText = '处理完成！';
                        break;
                    case 'failed':
                        statusText = `处理失败: ${progress.error_message || '未知错误'}`;
                        break;
                    default:
                        statusText = `状态: ${progress.status}`;
                }

                statusMessage.textContent = statusText;

                // Update the results container with current step if processing
                if (progress.status === 'processing' && progress.current_step) {
                    resultsContainer.innerHTML = `<div class="text-center">
                        <div class="spinner-border" role="status"></div>
                        <p class="mt-2">${progress.current_step}</p>
                    </div>`;
                }

                // If task is completed or failed, stop tracking
                if (progress.status === 'completed' || progress.status === 'failed') {
                    clearInterval(progressInterval);
                    progressInterval = null;

                    // If completed, load results
                    if (progress.status === 'completed') {
                        loadTaskResults(taskId);
                    } else {
                        resultsContainer.innerHTML = `<p class="text-center text-danger">处理失败: ${progress.error_message || '未知错误'}</p>`;
                    }

                    // Update task in history
                    updateTaskInHistory(taskId, progress.status);
                }
            } catch (error) {
                console.error('Progress tracking error:', error);
                statusMessage.textContent = `错误: ${error.message}`;
                resultsContainer.innerHTML = `<p class="text-center text-danger">检查任务状态时出错: ${error.message}</p>`;
                clearInterval(progressInterval);
                progressInterval = null;
            }
        }, 2000); // Check every 2 seconds instead of every second
    }

    /**
     * Load task results
     * @param {string} taskId - Task ID
     */
    async function loadTaskResults(taskId) {
        try {
            // Get task details
            const task = await api.getTask(taskId);

            // Clear results container
            resultsContainer.innerHTML = '';

            // Display output media
            if (task.output_media && task.output_media.length > 0) {
                for (const media of task.output_media) {
                    // Get media preview
                    const preview = await api.getMediaPreview(media.id);

                    // Create preview element
                    const previewElement = document.createElement('div');
                    previewElement.className = 'preview-container';

                    // Add preview content based on media type
                    switch (media.media_type) {
                        case 'text':
                            // 获取完整文本内容
                            fetch(`/api/v1/media/${media.id}/file`)
                                .then(response => response.text())
                                .then(fullText => {
                                    previewElement.innerHTML = `
                                        <h5>Text Output</h5>
                                        <div class="text-preview-container">
                                            <pre class="text-preview">${fullText}</pre>
                                        </div>
                                        <a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary mt-2" download>Download</a>
                                    `;
                                })
                                .catch(error => {
                                    console.error('Error fetching full text:', error);
                                    previewElement.innerHTML = `
                                        <h5>Text Output</h5>
                                        <pre>${preview.preview_url}</pre>
                                        <div class="alert alert-warning">无法加载完整文本内容</div>
                                        <a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary" download>Download</a>
                                    `;
                                });
                            break;

                        case 'image':
                            // Get the prompts from the task parameters if available
                            let promptsHtml = '';
                            if (task.parameters && (task.parameters.positive_prompt || task.parameters.negative_prompt)) {
                                promptsHtml = `
                                    <div class="prompts-container mt-2">
                                        <h6>Generated Prompts:</h6>
                                        <div class="prompt-box">
                                            <strong>Positive:</strong> ${task.parameters.positive_prompt || 'None'}
                                        </div>
                                        <div class="prompt-box">
                                            <strong>Negative:</strong> ${task.parameters.negative_prompt || 'None'}
                                        </div>
                                    </div>
                                `;
                            }

                            previewElement.innerHTML = `
                                <h5>Image Output</h5>
                                <img src="${preview.preview_url}" alt="Generated image">
                                ${promptsHtml}
                                <a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary mt-2" download>Download</a>
                            `;
                            break;

                        case 'audio':
                            previewElement.innerHTML = `
                                <h5>Audio Output</h5>
                                <audio controls src="${preview.preview_url}"></audio>
                                <a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary" download>Download</a>
                            `;
                            break;

                        case 'video':
                            previewElement.innerHTML = `
                                <h5>Video Output</h5>
                                <video controls src="${preview.preview_url}"></video>
                                <a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary" download>Download</a>
                            `;
                            break;

                        case 'model3d':
                            // For 3D models, we'll just show preview images if available
                            let previewContent = '<h5>3D Model Output</h5>';

                            if (Array.isArray(preview.preview_url)) {
                                previewContent += '<div class="model3d-preview">';
                                for (const url of preview.preview_url) {
                                    previewContent += `<img src="${url}" alt="3D model preview">`;
                                }
                                previewContent += '</div>';
                            } else {
                                previewContent += `<p>${preview.preview_url}</p>`;
                            }

                            previewContent += `<a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary" download>Download</a>`;
                            previewElement.innerHTML = previewContent;
                            break;

                        default:
                            previewElement.innerHTML = `
                                <h5>${media.media_type} Output</h5>
                                <p>Preview not available</p>
                                <a href="/api/v1/media/${media.id}/file" class="btn btn-sm btn-primary" download>Download</a>
                            `;
                    }

                    resultsContainer.appendChild(previewElement);
                }
            } else {
                resultsContainer.innerHTML = '<p class="text-center text-muted">No output media found</p>';
            }
        } catch (error) {
            console.error('Load results error:', error);
            resultsContainer.innerHTML = `<p class="text-center text-danger">Error loading results: ${error.message}</p>`;
        }
    }

    /**
     * Add task to history
     * @param {object} task - Task object
     */
    function addTaskToHistory(task) {
        // Add to local array
        if (task && task.id) {
            taskHistory.unshift({
                id: task.id,
                type: task.task_type || 'unknown',
                status: task.status || 'pending',
                created_at: task.created_at || new Date().toISOString()
            });
        } else {
            console.error('Invalid task object:', task);
            return;
        }

        // Limit history to 20 items
        if (taskHistory.length > 20) {
            taskHistory.pop();
        }

        // Save to localStorage
        localStorage.setItem('taskHistory', JSON.stringify(taskHistory));

        // Update UI
        loadTaskHistory();
    }

    /**
     * Update task in history
     * @param {string} taskId - Task ID
     * @param {string} status - Task status
     */
    function updateTaskInHistory(taskId, status) {
        // Find task in history
        const taskIndex = taskHistory.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            // Update status
            taskHistory[taskIndex].status = status;

            // Save to localStorage
            localStorage.setItem('taskHistory', JSON.stringify(taskHistory));

            // Update UI
            loadTaskHistory();
        }
    }

    /**
     * Load task history
     */
    function loadTaskHistory() {
        // Clear history table
        historyTable.innerHTML = '';

        // Add history items
        if (taskHistory.length === 0) {
            historyTable.innerHTML = '<tr><td colspan="5" class="text-center">No history</td></tr>';
            return;
        }

        for (const task of taskHistory) {
            const row = document.createElement('tr');
            row.className = 'history-item';
            row.dataset.taskId = task.id;

            // Format date
            const date = new Date(task.created_at);
            const formattedDate = date.toLocaleString();

            // Status badge
            let statusBadge = '';
            switch (task.status) {
                case 'pending':
                    statusBadge = '<span class="badge bg-secondary">Pending</span>';
                    break;
                case 'processing':
                    statusBadge = '<span class="badge bg-primary">Processing</span>';
                    break;
                case 'completed':
                    statusBadge = '<span class="badge bg-success">Completed</span>';
                    break;
                case 'failed':
                    statusBadge = '<span class="badge bg-danger">Failed</span>';
                    break;
                default:
                    statusBadge = `<span class="badge bg-secondary">${task.status}</span>`;
            }

            row.innerHTML = `
                <td>${task.id ? task.id.substring(0, 8) + '...' : 'N/A'}</td>
                <td>${task.type}</td>
                <td>${statusBadge}</td>
                <td>${formattedDate}</td>
                <td>
                    <button class="btn btn-sm btn-primary view-task-btn">View</button>
                </td>
            `;

            historyTable.appendChild(row);
        }

        // Add event listeners to view buttons
        const viewButtons = document.querySelectorAll('.view-task-btn');
        viewButtons.forEach(button => {
            button.addEventListener('click', function(event) {
                const taskId = event.target.closest('.history-item').dataset.taskId;
                viewTask(taskId);
            });
        });
    }

    /**
     * View task
     * @param {string} taskId - Task ID
     */
    async function viewTask(taskId) {
        if (!taskId) {
            statusMessage.textContent = "Error: Invalid task ID";
            return;
        }
        try {
            // Get task progress
            const progress = await api.getTaskProgress(taskId);

            // Update progress bar
            progressBar.style.width = `${progress.progress * 100}%`;
            progressBar.textContent = `${Math.round(progress.progress * 100)}%`;

            // Update status message
            statusMessage.textContent = `Status: ${progress.status}`;

            // If task is completed, load results
            if (progress.status === 'completed') {
                loadTaskResults(taskId);
            } else if (progress.status === 'failed') {
                statusMessage.textContent = `Error: ${progress.error_message || 'Unknown error'}`;
                resultsContainer.innerHTML = '<p class="text-center text-danger">Task failed</p>';
            } else {
                // Task is still in progress, start tracking
                currentTaskId = taskId;
                startProgressTracking(taskId);
                resultsContainer.innerHTML = '<p class="text-center text-muted">Processing...</p>';
            }
        } catch (error) {
            console.error('View task error:', error);
            statusMessage.textContent = `Error: ${error.message}`;
        }
    }
});
