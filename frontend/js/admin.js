/**
 * Admin JavaScript for TransMediaX frontend
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize API client
    const api = new TransMediaXAPI();

    // DOM elements
    const loginForm = document.getElementById('loginForm');
    const adminPanel = document.getElementById('adminPanel');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('loginBtn');
    const loginError = document.getElementById('loginError');
    const sttApiKeyInput = document.getElementById('sttApiKey');
    const sttBaseUrlInput = document.getElementById('sttBaseUrl');
    const sttModelInput = document.getElementById('sttModel');
    const ttsApiKeyInput = document.getElementById('ttsApiKey');
    const ttsBaseUrlInput = document.getElementById('ttsBaseUrl');
    const ttsModelInput = document.getElementById('ttsModel');
    const llmApiKeyInput = document.getElementById('llmApiKey');
    const llmBaseUrlInput = document.getElementById('llmBaseUrl');
    const llmModelInput = document.getElementById('llmModel');
    const comfyuiBaseUrlInput = document.getElementById('comfyuiBaseUrl');
    const saveConfigBtn = document.getElementById('saveConfigBtn');
    const configSaveSuccess = document.getElementById('configSaveSuccess');
    const configSaveError = document.getElementById('configSaveError');
    const convertersTable = document.getElementById('convertersTable');
    const databasePathInput = document.getElementById('databasePath');
    const mediaStoragePathInput = document.getElementById('mediaStoragePath');
    const restartBtn = document.getElementById('restartBtn');

    // Admin credentials
    let adminUsername = '';
    let adminPassword = '';

    // Event listeners
    loginBtn.addEventListener('click', login);
    saveConfigBtn.addEventListener('click', saveConfig);
    restartBtn.addEventListener('click', restartServer);

    // Check if we have stored credentials
    const storedUsername = localStorage.getItem('adminUsername');
    const storedPassword = localStorage.getItem('adminPassword');

    if (storedUsername && storedPassword) {
        usernameInput.value = storedUsername;
        passwordInput.value = storedPassword;
        login();
    }

    /**
     * Login to admin panel
     */
    async function login() {
        // Get credentials
        adminUsername = usernameInput.value.trim();
        adminPassword = passwordInput.value.trim();

        if (!adminUsername || !adminPassword) {
            loginError.textContent = 'Username and password are required';
            loginError.classList.remove('d-none');
            return;
        }

        try {
            // Try to get config
            const config = await api.getAdminConfig(adminUsername, adminPassword);

            // Store credentials
            localStorage.setItem('adminUsername', adminUsername);
            localStorage.setItem('adminPassword', adminPassword);

            // Hide login form and show admin panel
            loginForm.classList.add('d-none');
            adminPanel.classList.remove('d-none');

            // Populate config fields
            // 对于API密钥字段，添加一个提示文本
            sttApiKeyInput.value = config.STT_API_KEY || '';
            if (sttApiKeyInput.value === '********') {
                sttApiKeyInput.placeholder = '保持不变请留空，或输入新的API密钥';
            }

            sttBaseUrlInput.value = config.STT_BASE_URL || '';
            sttModelInput.value = config.STT_MODEL || '';

            ttsApiKeyInput.value = config.TTS_API_KEY || '';
            if (ttsApiKeyInput.value === '********') {
                ttsApiKeyInput.placeholder = '保持不变请留空，或输入新的API密钥';
            }

            ttsBaseUrlInput.value = config.TTS_BASE_URL || '';
            ttsModelInput.value = config.TTS_MODEL || '';

            llmApiKeyInput.value = config.LLM_API_KEY || '';
            if (llmApiKeyInput.value === '********') {
                llmApiKeyInput.placeholder = '保持不变请留空，或输入新的API密钥';
            }

            llmBaseUrlInput.value = config.LLM_BASE_URL || '';
            llmModelInput.value = config.LLM_MODEL || '';
            comfyuiBaseUrlInput.value = config.COMFYUI_BASE_URL || '';
            databasePathInput.value = config.DATABASE_PATH || '';
            mediaStoragePathInput.value = config.MEDIA_STORAGE_PATH || '';

            // Load converters
            loadConverters();
        } catch (error) {
            console.error('Login error:', error);
            loginError.textContent = 'Invalid credentials';
            loginError.classList.remove('d-none');
        }
    }

    /**
     * Save configuration
     */
    async function saveConfig() {
        try {
            // Hide messages
            configSaveSuccess.classList.add('d-none');
            configSaveError.classList.add('d-none');

            // 处理API密钥字段
            // 如果用户清空了字段，则发送空字符串
            // 如果用户没有修改字段（仍然是星号），则保持星号
            function processApiKey(input) {
                const value = input.value.trim();
                if (value === '') {
                    return ''; // 用户清空了字段，发送空字符串
                }
                return value; // 保持用户输入的值（可能是新的API密钥或星号）
            }

            // Get config values
            const config = {
                STT_API_KEY: processApiKey(sttApiKeyInput),
                STT_BASE_URL: sttBaseUrlInput.value.trim(),
                STT_MODEL: sttModelInput.value.trim(),
                TTS_API_KEY: processApiKey(ttsApiKeyInput),
                TTS_BASE_URL: ttsBaseUrlInput.value.trim(),
                TTS_MODEL: ttsModelInput.value.trim(),
                LLM_API_KEY: processApiKey(llmApiKeyInput),
                LLM_BASE_URL: llmBaseUrlInput.value.trim(),
                LLM_MODEL: llmModelInput.value.trim(),
                COMFYUI_BASE_URL: comfyuiBaseUrlInput.value.trim()
            };

            // Update config
            await api.updateAdminConfig(config, adminUsername, adminPassword);

            // Show success message
            configSaveSuccess.classList.remove('d-none');

            // Reload converters after a short delay
            setTimeout(loadConverters, 2000);
        } catch (error) {
            console.error('Save config error:', error);
            configSaveError.textContent = `Error: ${error.message}`;
            configSaveError.classList.remove('d-none');
        }
    }

    /**
     * Load converters
     */
    async function loadConverters() {
        try {
            // Get converters
            const converters = await api.listConverters(adminUsername, adminPassword);

            // Clear converters table
            convertersTable.innerHTML = '';

            // Add converters
            if (converters.length === 0) {
                convertersTable.innerHTML = '<tr><td colspan="4" class="text-center">No converters available</td></tr>';
                return;
            }

            for (const converter of converters) {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${converter.name}</td>
                    <td>${converter.description}</td>
                    <td>${converter.input_types.join(', ')}</td>
                    <td>${converter.output_types.join(', ')}</td>
                `;

                convertersTable.appendChild(row);
            }
        } catch (error) {
            console.error('Load converters error:', error);
            convertersTable.innerHTML = `<tr><td colspan="4" class="text-center text-danger">Error loading converters: ${error.message}</td></tr>`;
        }
    }

    /**
     * Restart server
     */
    async function restartServer() {
        if (confirm('Are you sure you want to restart the server?')) {
            try {
                await api.request('/admin/restart', 'POST', null, {
                    'Authorization': `Basic ${btoa(`${adminUsername}:${adminPassword}`)}`
                });

                alert('Server restart initiated. The page will reload in 5 seconds.');

                // Reload page after 5 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
            } catch (error) {
                console.error('Restart server error:', error);
                alert(`Error restarting server: ${error.message}`);
            }
        }
    }
});
