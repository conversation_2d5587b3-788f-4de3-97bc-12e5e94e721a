credentials_for_provider:
  base_url:
    label:
      en_US: The URL of ComfyUI Server
      zh_Hans: ComfyUI服务器的URL
    placeholder:
      en_US: Please input your ComfyUI server's Base URL
      zh_Hans: 请输入你的 ComfyUI 服务器的 Base URL
    required: true
    type: text-input
    url: https://docs.dify.ai/guides/tools/tool-configuration/comfyui
  image_server_url:
    label:
      en_US: The URL of Dify API endpoint 
      zh_Hans: The URL of Dify API endpoint 
    placeholder:
      en_US: Please input your API endpoint. For example, https://app.dify.ai:8080
      zh_Hans: 请输入你的 API endpoint 例子 https://app.dify.ai:8080
    required: true
    type: text-input
extra:
  python:
    source: provider/comfyui.py
identity:
  author: Qun
  description:
    en_US: ComfyUI is a tool for generating images which can be deployed locally.
    zh_Hans: ComfyUI 是一个可以在本地部署的图片生成的工具。
  icon: icon.png
  label:
    en_US: ComfyUI
    zh_Hans: ComfyUI
  name: comfyui
  tags:
  - image
tools:
- tools/comfyui_workflow.yaml
- tools/comfyui_txt2img.yaml
- tools/comfyui_img2img.yaml
- tools/comfyui_img2vid.yaml
- tools/comfyui_depth_anything.yaml
- tools/comfyui_depth_pro.yaml
- tools/comfyui_face_swap.yaml
- tools/comfyui_list_models.yaml
- tools/comfyui_list_samplers.yaml
