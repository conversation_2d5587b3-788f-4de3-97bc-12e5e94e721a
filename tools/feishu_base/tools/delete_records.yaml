description:
  human:
    en_US: Delete Multiple Records from Multidimensional Table
    zh_Hans: 删除多维表格数据表中的多条记录
  llm: A tool for deleting multiple records from a multidimensional table. (删除多维表格数据表中的多条记录)
extra:
  python:
    source: tools/delete_records.py
identity:
  author: <PERSON> Lea
  label:
    en_US: Delete Records
    zh_Hans: 删除多条记录
  name: delete_records
parameters:
- form: llm
  human_description:
    en_US: Unique identifier for the multidimensional table, supports inputting document
      URL.
    zh_Hans: 多维表格的唯一标识符，支持输入文档 URL。
  label:
    en_US: app_token
    zh_Hans: app_token
  llm_description: 多维表格的唯一标识符，支持输入文档 URL。
  name: app_token
  required: true
  type: string
- form: llm
  human_description:
    en_US: Unique identifier for the multidimensional table data, either table_id
      or table_name must be provided, cannot be empty simultaneously.
    zh_Hans: 多维表格数据表的唯一标识符，table_id 和 table_name 至少需要提供一个，不能同时为空。
  label:
    en_US: table_id
    zh_Hans: table_id
  llm_description: 多维表格数据表的唯一标识符，table_id 和 table_name 至少需要提供一个，不能同时为空。
  name: table_id
  required: false
  type: string
- form: llm
  human_description:
    en_US: Name of the multidimensional table data, either table_name or table_id
      must be provided, cannot be empty simultaneously.
    zh_Hans: 多维表格数据表的名称，table_name 和 table_id 至少需要提供一个，不能同时为空。
  label:
    en_US: table_name
    zh_Hans: table_name
  llm_description: 多维表格数据表的名称，table_name 和 table_id 至少需要提供一个，不能同时为空。
  name: table_name
  required: false
  type: string
- form: llm
  human_description:
    en_US: 'List of IDs for the records to be deleted, example value: ["recwNXzPQv"].

      '
    zh_Hans: 删除的多条记录 ID 列表，示例值：["recwNXzPQv"]。
  label:
    en_US: Record IDs
    zh_Hans: 记录 ID 列表
  llm_description: 删除的多条记录 ID 列表，示例值：["recwNXzPQv"]。
  name: record_ids
  required: true
  type: string
- default: open_id
  form: form
  human_description:
    en_US: User ID type, optional values are open_id, union_id, user_id, with a default
      value of open_id.
    zh_Hans: 用户 ID 类型，可选值有 open_id、union_id、user_id，默认值为 open_id。
  label:
    en_US: user_id_type
    zh_Hans: 用户 ID 类型
  llm_description: 用户 ID 类型，可选值有 open_id、union_id、user_id，默认值为 open_id。
  name: user_id_type
  options:
  - label:
      en_US: open_id
      zh_Hans: open_id
    value: open_id
  - label:
      en_US: union_id
      zh_Hans: union_id
    value: union_id
  - label:
      en_US: user_id
      zh_Hans: user_id
    value: user_id
  required: false
  type: select
