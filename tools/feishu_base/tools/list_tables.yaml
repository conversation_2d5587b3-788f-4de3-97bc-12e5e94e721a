description:
  human:
    en_US: Get All Data Tables under Multidimensional Table
    zh_Hans: 获取多维表格下的所有数据表
  llm: A tool for getting all data tables under a multidimensional table. (获取多维表格下的所有数据表)
extra:
  python:
    source: tools/list_tables.py
identity:
  author: <PERSON>
  label:
    en_US: List Tables
    zh_Hans: 列出数据表
  name: list_tables
parameters:
- form: llm
  human_description:
    en_US: Unique identifier for the multidimensional table, supports inputting document
      URL.
    zh_Hans: 多维表格的唯一标识符，支持输入文档 URL。
  label:
    en_US: app_token
    zh_Hans: app_token
  llm_description: 多维表格的唯一标识符，支持输入文档 URL。
  name: app_token
  required: true
  type: string
- default: 20
  form: form
  human_description:
    en_US: 'Page size, default value: 20, maximum value: 100.

      '
    zh_Hans: 分页大小，默认值：20，最大值：100。
  label:
    en_US: page_size
    zh_Hans: 分页大小
  llm_description: 分页大小，默认值：20，最大值：100。
  name: page_size
  required: false
  type: number
- form: llm
  human_description:
    en_US: 'Page token, leave empty for the first request to start from the beginning;
      a new page_token will be returned if there are more items in the paginated query
      results, which can be used for the next traversal. Example value: "tblsRc9GRRXKqhvW".

      '
    zh_Hans: 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token
      获取查询结果。示例值："tblsRc9GRRXKqhvW"。
  label:
    en_US: page_token
    zh_Hans: 分页标记
  llm_description: 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token
    获取查询结果。示例值："tblsRc9GRRXKqhvW"。
  name: page_token
  required: false
  type: string
