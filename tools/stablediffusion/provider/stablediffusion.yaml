credentials_for_provider:
  base_url:
    label:
      en_US: Base URL
      pt_BR: Base URL
      zh_Hans: StableDiffusion服务器的Base URL
    placeholder:
      en_US: Please input your StableDiffusion server's Base URL
      pt_BR: Please input your StableDiffusion server's Base URL
      zh_Hans: 请输入你的 StableDiffusion 服务器的 Base URL
    required: true
    type: secret-input
  model:
    help:
      en_US: The model name of the StableDiffusion server
      pt_BR: The model name of the StableDiffusion server
      zh_Hans: StableDiffusion服务器的模型名称
    label:
      en_US: Model
      pt_BR: Model
      zh_Hans: 模型
    placeholder:
      en_US: Please input your model
      pt_BR: Please input your model
      zh_Hans: 请输入你的模型名称
    required: true
    type: text-input
    url: https://docs.dify.ai/tutorials/tool-configuration/stable-diffusion
extra:
  python:
    source: provider/stablediffusion.py
identity:
  author: langgenius
  description:
    en_US: Stable Diffusion is a tool for generating images which can be deployed
      locally.
    pt_BR: Stable Diffusion is a tool for generating images which can be deployed
      locally.
    zh_Hans: Stable Diffusion 是一个可以在本地部署的图片生成的工具。
  icon: icon.png
  label:
    en_US: Stable Diffusion
    pt_BR: Stable Diffusion
    zh_Hans: Stable Diffusion
  name: stablediffusion
  tags:
  - image
tools:
- tools/stable_diffusion.yaml
