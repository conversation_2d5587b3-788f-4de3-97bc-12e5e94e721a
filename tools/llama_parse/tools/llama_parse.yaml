identity:
  name: llama_parse
  author: langgenius
  label:
    en_US: llama parse
    zh_Hans: llama parse
description:
  human:
    en_US: a tool for parsing text, tables, and images, supporting multiple formats such as pdf, pptx, docx, etc. supporting multiple languages such as English, Chinese, etc.
    zh_Hans: 一个用于解析文本，表格和图片的工具，支持pdf,pptx,docx等多种格式。支持英语，中文等多种语言
  llm: a tool for parsing text, tables, and images, supporting multiple formats such as pdf, pptx, docx, etc. supporting multiple languages such as English, Chinese, etc.
parameters:
  - name: files
    type: files
    required: true
    label:
      en_US: upload file
      zh_Hans: 上传文件
    human_description:
      en_US: upload the file to be parsed
      zh_Hans: 上传用于解析的文件
    llm_description: upload the file to be parsed
    form: llm
  - name: result_type
    type: select
    required: false
    default: markdown
    label:
      en_US: result form type
      zh_Hans: 结果格式类型
    human_description:
      en_US: the type of the result
      zh_Hans: 选择结果的格式类型
    options:
      - label:
          en_US: txt
          zh_Hans: txt
        value: text
      - label:
          en_US: md
          zh_Hans: md
        value: markdown
    llm_description: the type of the result
    form: form
  - name: num_workers
    type: number
    required: false
    default: 4
    llm_description: the number of workers
    label:
      en_US: number of workers
      zh_Hans: 工作线程数
    human_description:
      en_US: if multiple files passed, split in `num_workers` API calls
      zh_Hans: 如果传递了多个文件，则在`num_workers` API调用中拆分
    form: form
  - name: verbose
    type: boolean
    required: false
    default: false
    llm_description: whether to output the verbose information
    label:
      en_US: verbose
      zh_Hans: 详情
    human_description:
      en_US: whether to output the verbose information
      zh_Hans: 是否输出详细信息
    form: form
  - name: language
    type: string
    required: false
    default: en
    llm_description: the language of the output file
    label:
      en_US: language
      zh_Hans: 语言
    human_description:
      en_US: Optionally you can define a language, default=en
      zh_Hans: 可选地，您可以定义一种语言，默认=en
    form: form

extra:
  python:
    source: tools/llama_parse.py
