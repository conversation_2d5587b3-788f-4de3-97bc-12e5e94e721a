description:
  human:
    en_US: Fetch the target URL (can be a PDF) and convert it into a LLM-friendly
      markdown.
    pt_BR: Busque a URL de destino (que pode ser um PDF) e converta em um Markdown
      LLM-friendly.
    zh_Hans: 获取目标网址（可以是 PDF），并将其转换为适合大模型处理的 Markdown 格式。
  llm: A tool for scraping webpages. Input should be a URL.
extra:
  python:
    source: tools/jina_reader.py
identity:
  author: langgenius
  label:
    en_US: Fetch Single Page
    pt_BR: Fetch Single Page
    zh_Hans: 获取单页面
  name: jina_reader
parameters:
- form: llm
  human_description:
    en_US: Web link
    pt_BR: URL da web
    zh_Hans: 网页链接
  label:
    en_US: URL
    pt_BR: URL
    zh_Hans: 网址
  llm_description: url para scraping
  name: url
  required: true
  type: string
- form: llm
  human_description:
    en_US: 'request parameters, format: {"key1": "value1", "key2": "value2"}

      '
    pt_BR: 'parâmetros de solicitação, formato: {"key1": "value1", "key2": "value2"}

      '
    zh_Hans: '请求参数，格式：{"key1": "value1", "key2": "value2"}

      '
  label:
    en_US: Request params
    pt_BR: Parâmetros de solicitação
    zh_Hans: 请求参数
  llm_description: request parameters
  name: request_params
  required: false
  type: string
- form: form
  human_description:
    en_US: css selector for scraping specific elements
    pt_BR: css selector para scraping de elementos específicos
    zh_Hans: css 选择器用于抓取特定元素
  label:
    en_US: Target selector
    pt_BR: Seletor de destino
    zh_Hans: 目标选择器
  llm_description: css selector of the target element to scrape
  name: target_selector
  required: false
  type: string
- form: form
  human_description:
    en_US: css selector for waiting for specific elements
    pt_BR: css selector para aguardar elementos específicos
    zh_Hans: css 选择器用于等待特定元素
  label:
    en_US: Wait for selector
    pt_BR: Aguardar por seletor
    zh_Hans: 等待选择器
  llm_description: css selector of the target element to wait for
  name: wait_for_selector
  required: false
  type: string
- form: form
  human_description:
    en_US: Remove all images from the response.
    zh_Hans: 从响应中删除所有图片。
  label:
    en_US: Remove images
    zh_Hans: 删除图片
  llm_description: Remove all images from the response
  name: remove_images
  required: false
  type: boolean
- default: false
  form: form
  human_description:
    en_US: 'Captions all images at the specified URL, adding ''Image [idx]: [caption]''
      as an alt tag for those without one. This allows downstream LLMs to interact
      with the images in activities such as reasoning and summarizing.'
    pt_BR: 'Adiciona legendas a todas as imagens na URL especificada, adicionando
      ''Imagem [idx]: [legenda]'' como uma tag alt para aquelas que não têm uma. Isso
      permite que os modelos LLM inferiores interajam com as imagens em atividades
      como raciocínio e resumo.'
    zh_Hans: '为指定 URL 上的所有图像添加标题，为没有标题的图像添加“Image [idx]: [caption]”作为 alt 标签，以支持下游模型的图像交互。'
  label:
    en_US: Image caption
    pt_BR: Legenda da imagem
    zh_Hans: 图片说明
  llm_description: Captions all images at the specified URL
  name: image_caption
  required: false
  type: boolean
- default: false
  form: form
  human_description:
    en_US: A "Buttons & Links" section will be created at the end. This helps the
      downstream LLMs or web agents navigating the page or take further actions.
    pt_BR: Um "Botões & Links" section will be created at the end. This helps the
      downstream LLMs or web agents navigating the page or take further actions.
    zh_Hans: 末尾将添加“按钮和链接”部分，方便下游模型或网络代理做页面导航或执行进一步操作。
  label:
    en_US: Gather all links at the end
    pt_BR: Coletar todos os links ao final
    zh_Hans: 将所有链接集中到最后
  llm_description: Gather all links at the end
  name: gather_all_links_at_the_end
  required: false
  type: boolean
- default: false
  form: form
  human_description:
    en_US: An "Images" section will be created at the end. This gives the downstream
      LLMs an overview of all visuals on the page, which may improve reasoning.
    pt_BR: Um "Imagens" section will be created at the end. This gives the downstream
      LLMs an overview of all visuals on the page, which may improve reasoning.
    zh_Hans: 末尾会新增“图片”部分，方便下游模型全面了解页面的视觉内容，提升推理效果。
  label:
    en_US: Gather all images at the end
    pt_BR: Coletar todas as imagens ao final
    zh_Hans: 将所有图片集中到最后
  llm_description: Gather all images at the end
  name: gather_all_images_at_the_end
  required: false
  type: boolean
- form: form
  human_description:
    en_US: Use proxy to access URLs
    pt_BR: Use proxy to access URLs
    zh_Hans: 利用代理访问 URL
  label:
    en_US: Proxy server
    pt_BR: Servidor de proxy
    zh_Hans: 代理服务器
  llm_description: Use proxy to access URLs
  name: proxy_server
  required: false
  type: string
- default: false
  form: form
  human_description:
    en_US: Bypass the Cache
    pt_BR: Ignorar o cache
    zh_Hans: 是否绕过缓存
  label:
    en_US: Bypass the Cache
    pt_BR: Ignorar o cache
    zh_Hans: 绕过缓存
  llm_description: bypass the cache
  name: no_cache
  required: false
  type: boolean
- default: false
  form: form
  human_description:
    en_US: When enabled, request results won't be cached on jina servers.
    zh_Hans: 启用后，请求结果将不会被缓存在 jina 服务器上。
  label:
    en_US: Do Not Cache/Track
    zh_Hans: 请勿缓存/追踪
  llm_description: Do Not Cache/Track
  name: no_cache_track
  required: false
  type: boolean
- default: false
  form: form
  human_description:
    en_US: Enable summary for the output
    pt_BR: Habilitar resumo para a saída
    zh_Hans: 为输出启用摘要
  label:
    en_US: Enable summary
    pt_BR: Habilitar resumo
    zh_Hans: 是否启用摘要
  llm_description: enable summary
  name: summary
  required: false
  type: boolean
- default: 3
  form: form
  human_description:
    en_US: Number of times to retry the request if it fails
    pt_BR: Número de vezes para repetir a solicitação se falhar
    zh_Hans: 请求失败时重试的次数
  label:
    en_US: Retry
    pt_BR: Repetir
    zh_Hans: 重试
  llm_description: Number of times to retry the request if it fails
  name: max_retries
  required: false
  type: number
