description:
  human:
    en_US: Split long text into chunks and do tokenization.
    pt_BR: Dividir o texto longo em pedaços e fazer tokenização.
    zh_Hans: 将长文本拆分成小段落，并做分词处理。
  llm: Free API to tokenize text and segment long text into chunks.
extra:
  python:
    source: tools/jina_tokenizer.py
identity:
  author: hjlarry
  label:
    en_US: Segment
    pt_BR: Segment
    zh_Hans: 切分器
  name: jina_tokenizer
parameters:
- form: llm
  human_description:
    en_US: The content which need to tokenize or segment.
    pt_BR: O conteúdo que precisa ser tokenizado ou segmentado.
    zh_Hans: 需要分词或分段的内容。
  label:
    en_US: Content
    pt_BR: Conteúdo
    zh_Hans: 内容
  llm_description: the content which need to tokenize or segment
  name: content
  required: true
  type: string
- form: form
  human_description:
    en_US: Return the tokens and their corresponding ids in the response.
    pt_BR: Retornar os tokens e seus respectivos ids na resposta.
    zh_Hans: 返回tokens及其对应的ids。
  label:
    en_US: Return the tokens
    pt_BR: Retornar os tokens
    zh_Hans: 是否返回tokens
  name: return_tokens
  required: false
  type: boolean
- form: form
  human_description:
    en_US: Chunking the input into semantically meaningful segments while handling
      a wide variety of text types and edge cases based on common structural cues.
    pt_BR: Dividir o texto de entrada em segmentos semanticamente significativos,
      enquanto lida com uma ampla variedade de tipos de texto e casos de borda com
      base em pistas estruturais comuns.
    zh_Hans: 将输入文本分块为语义有意义的片段，同时基于常见的结构线索处理各种文本类型和特殊情况。
  label:
    en_US: Return the chunks
    pt_BR: Retornar os chunks
    zh_Hans: 是否分块
  name: return_chunks
  type: boolean
- form: form
  human_description:
    en_US: '· cl100k_base --- gpt-4, gpt-3.5-turbo, gpt-3.5

      · o200k_base  --- gpt-4o, gpt-4o-mini

      · p50k_base   --- text-davinci-003, text-davinci-002

      · r50k_base   --- text-davinci-001, text-curie-001

      · p50k_edit   --- text-davinci-edit-001, code-davinci-edit-001

      · gpt2        --- gpt-2

      '
  label:
    en_US: Tokenizer
  name: tokenizer
  options:
  - label:
      en_US: cl100k_base
    value: cl100k_base
  - label:
      en_US: o200k_base
    value: o200k_base
  - label:
      en_US: p50k_base
    value: p50k_base
  - label:
      en_US: r50k_base
    value: r50k_base
  - label:
      en_US: p50k_edit
    value: p50k_edit
  - label:
      en_US: gpt2
    value: gpt2
  type: select
