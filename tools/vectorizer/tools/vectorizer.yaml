description:
  human:
    en_US:
      Convert your PNG and JPG images to SVG vectors quickly and easily. Fully
      automatically. Using AI.
    zh_Hans: 一个将 PNG 和 JPG 图像快速轻松地转换为 SVG 矢量图的工具。
  llm:
    A tool for converting images to SVG vectors. you should input the image id
    as the input of this tool. the image id can be got from parameters.
extra:
  python:
    source: tools/vectorizer.py
identity:
  author: langgenius
  label:
    en_US: Vectorizer.AI
    zh_Hans: Vectorizer.AI
  name: vectorizer
parameters:
  - form: llm
    human_description:
      en_US: The image to be converted.
      zh_Hans: 要转换的图片。
    label:
      en_US: image
    llm_description: you should not input this parameter. just input the image_id.
    name: image
    type: file
  - default: test
    form: form
    human_description:
      en_US:
        It is free to integrate with and test out the API in test mode, no subscription
        required.
      zh_Hans: 在测试模式下，可以免费测试API。
    label:
      en_US: Mode
      zh_Hans: 模式
    name: mode
    options:
      - label:
          en_US: production
          zh_Hans: 生产模式
        value: production
      - label:
          en_US: test
          zh_Hans: 测试模式
        value: test
    required: true
    type: select
