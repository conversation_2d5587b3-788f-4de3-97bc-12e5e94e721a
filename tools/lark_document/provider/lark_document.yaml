credentials_for_provider:
  app_id:
    help:
      en_US: Get your app_id and app_secret from Lark
      zh_Hans: 从 Lark 获取您的 app_id 和 app_secret
    label:
      en_US: APP ID
    placeholder:
      en_US: Please input your Lark app id
      zh_Hans: 请输入你的 Lark app id
    required: true
    type: text-input
    url: https://open.larksuite.com/app
  app_secret:
    label:
      en_US: APP Secret
    placeholder:
      en_US: Please input your app secret
      zh_Hans: 请输入你的 Lark app secret
    required: true
    type: secret-input
extra:
  python:
    source: provider/lark_document.py
identity:
  author: langgenius
  description:
    en_US: 'Lark cloud document, requires the following permissions: docx:document、drive:drive、docs:document.content:read.

      '
    zh_Hans: 'Lark 云文档，需要开通以下权限: docx:document、drive:drive、docs:document.content:read。

      '
  icon: icon.svg
  label:
    en_US: Lark Cloud Document
    zh_Hans: Lark 云文档
  name: lark_document
  tags:
  - social
  - productivity
tools:
- tools/create_document.yaml
- tools/get_document_content.yaml
- tools/write_document.yaml
- tools/list_document_blocks.yaml
