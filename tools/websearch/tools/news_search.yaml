description:
  human:
    en_US: A tool to retrieve organic search results snippets and links from Google
      News engine.
    zh_Hans: 一种从 Google 新闻引擎检索有机搜索结果片段和链接的工具。
  llm: A tool to retrieve organic search results snippets and links from Google News
    engine.
extra:
  python:
    source: tools/news_search.py
identity:
  author: langgenius
  label:
    en_US: News Search API
    zh_Hans: News Search API
  name: news_search
parameters:
- form: llm
  human_description:
    en_US: Defines the query you want to search.
    zh_Hans: 定义您要搜索的查询。
  label:
    en_US: Query
    zh_Hans: 询问
  llm_description: Defines the search query you want to search.
  name: query
  required: true
  type: string
- default: US
  form: form
  human_description:
    en_US: Defines from where you want the search to originate. (For example - New
      York)
    zh_Hans: 定义您想要搜索的起始位置。 （例如 - 纽约）
  label:
    en_US: Location
    zh_Hans: 询问
  llm_description: Defines from where you want the search to originate. (For example
    - New York)
  name: location
  options:
  - label:
      en_US: Australia
      pt_BR: Australia
      zh_Hans: 澳大利亚
    value: AU
  - label:
      en_US: Brazil
      pt_BR: Brazil
      zh_Hans: 巴西
    value: BR
  - label:
      en_US: Canada
      pt_BR: Canada
      zh_Hans: 加拿大
    value: CA
  - label:
      en_US: Germany
      pt_BR: Germany
      zh_Hans: 德国
    value: DE
  - label:
      en_US: France
      pt_BR: France
      zh_Hans: 法国
    value: FR
  - label:
      en_US: United Kingdom
      pt_BR: United Kingdom
      zh_Hans: 英国
    value: GB
  - label:
      en_US: United States
      pt_BR: United States
      zh_Hans: 美国
    value: US
  - label:
      en_US: Japan
      pt_BR: Japan
      zh_Hans: 日本
    value: JP
  - label:
      en_US: India
      pt_BR: India
      zh_Hans: 印度
    value: IN
  - label:
      en_US: Korea
      pt_BR: Korea
      zh_Hans: 韩国
    value: KR
  - label:
      en_US: Singapore
      pt_BR: Singapore
      zh_Hans: 新加坡
    value: SG
  - label:
      en_US: Sweden
      pt_BR: Sweden
      zh_Hans: 瑞典
    value: SE
  required: false
  type: string
- default: US
  form: form
  human_description:
    en_US: Defines the country of the search. Default is "US".
    zh_Hans: 定义搜索的国家/地区。默认为“美国”。
  label:
    en_US: Country
    zh_Hans: 国家/地区
  llm_description: Defines the gl parameter of the Google search.
  name: gl
  options:
  - label:
      en_US: Argentina
      pt_BR: Argentina
      zh_Hans: 阿根廷
    value: AR
  - label:
      en_US: Australia
      pt_BR: Australia
      zh_Hans: 澳大利亚
    value: AU
  - label:
      en_US: Austria
      pt_BR: Austria
      zh_Hans: 奥地利
    value: AT
  - label:
      en_US: Belgium
      pt_BR: Belgium
      zh_Hans: 比利时
    value: BE
  - label:
      en_US: Brazil
      pt_BR: Brazil
      zh_Hans: 巴西
    value: BR
  - label:
      en_US: Canada
      pt_BR: Canada
      zh_Hans: 加拿大
    value: CA
  - label:
      en_US: Chile
      pt_BR: Chile
      zh_Hans: 智利
    value: CL
  - label:
      en_US: Colombia
      pt_BR: Colombia
      zh_Hans: 哥伦比亚
    value: CO
  - label:
      en_US: China
      pt_BR: China
      zh_Hans: 中国
    value: CN
  - label:
      en_US: Czech Republic
      pt_BR: Czech Republic
      zh_Hans: 捷克共和国
    value: CZ
  - label:
      en_US: Denmark
      pt_BR: Denmark
      zh_Hans: 丹麦
    value: DK
  - label:
      en_US: Finland
      pt_BR: Finland
      zh_Hans: 芬兰
    value: FI
  - label:
      en_US: France
      pt_BR: France
      zh_Hans: 法国
    value: FR
  - label:
      en_US: Germany
      pt_BR: Germany
      zh_Hans: 德国
    value: DE
  - label:
      en_US: Hong Kong
      pt_BR: Hong Kong
      zh_Hans: 香港
    value: HK
  - label:
      en_US: India
      pt_BR: India
      zh_Hans: 印度
    value: IN
  - label:
      en_US: Indonesia
      pt_BR: Indonesia
      zh_Hans: 印度尼西亚
    value: ID
  - label:
      en_US: Italy
      pt_BR: Italy
      zh_Hans: 意大利
    value: IT
  - label:
      en_US: Japan
      pt_BR: Japan
      zh_Hans: 日本
    value: JP
  - label:
      en_US: Korea
      pt_BR: Korea
      zh_Hans: 韩国
    value: KR
  - label:
      en_US: Malaysia
      pt_BR: Malaysia
      zh_Hans: 马来西亚
    value: MY
  - label:
      en_US: Mexico
      pt_BR: Mexico
      zh_Hans: 墨西哥
    value: MX
  - label:
      en_US: Netherlands
      pt_BR: Netherlands
      zh_Hans: 荷兰
    value: NL
  - label:
      en_US: New Zealand
      pt_BR: New Zealand
      zh_Hans: 新西兰
    value: NZ
  - label:
      en_US: Norway
      pt_BR: Norway
      zh_Hans: 挪威
    value: false
  - label:
      en_US: Philippines
      pt_BR: Philippines
      zh_Hans: 菲律宾
    value: PH
  - label:
      en_US: Poland
      pt_BR: Poland
      zh_Hans: 波兰
    value: PL
  - label:
      en_US: Portugal
      pt_BR: Portugal
      zh_Hans: 葡萄牙
    value: PT
  - label:
      en_US: Russia
      pt_BR: Russia
      zh_Hans: 俄罗斯
    value: RU
  - label:
      en_US: Saudi Arabia
      pt_BR: Saudi Arabia
      zh_Hans: 沙特阿拉伯
    value: SA
  - label:
      en_US: Singapore
      pt_BR: Singapore
      zh_Hans: 新加坡
    value: SG
  - label:
      en_US: South Africa
      pt_BR: South Africa
      zh_Hans: 南非
    value: ZA
  - label:
      en_US: Spain
      pt_BR: Spain
      zh_Hans: 西班牙
    value: ES
  - label:
      en_US: Sweden
      pt_BR: Sweden
      zh_Hans: 瑞典
    value: SE
  - label:
      en_US: Switzerland
      pt_BR: Switzerland
      zh_Hans: 瑞士
    value: CH
  - label:
      en_US: Taiwan
      pt_BR: Taiwan
      zh_Hans: 台湾
    value: TW
  - label:
      en_US: Thailand
      pt_BR: Thailand
      zh_Hans: 泰国
    value: TH
  - label:
      en_US: Turkey
      pt_BR: Turkey
      zh_Hans: 土耳其
    value: TR
  - label:
      en_US: United Kingdom
      pt_BR: United Kingdom
      zh_Hans: 英国
    value: GB
  - label:
      en_US: United States
      pt_BR: United States
      zh_Hans: 美国
    value: US
  required: false
  type: select
- default: en
  form: form
  human_description:
    en_US: Defines the interface language of the search. Default is "en".
    zh_Hans: 定义搜索的界面语言。默认为“en”。
  label:
    en_US: Language
    zh_Hans: 语言
  name: hl
  options:
  - label:
      en_US: Arabic
      zh_Hans: 阿拉伯语
    value: ar
  - label:
      en_US: Bulgarian
      zh_Hans: 保加利亚语
    value: bg
  - label:
      en_US: Catalan
      zh_Hans: 加泰罗尼亚语
    value: ca
  - label:
      en_US: Chinese (Simplified)
      zh_Hans: 中文（简体）
    value: zh-cn
  - label:
      en_US: Chinese (Traditional)
      zh_Hans: 中文（繁体）
    value: zh-tw
  - label:
      en_US: Czech
      zh_Hans: 捷克语
    value: cs
  - label:
      en_US: Danish
      zh_Hans: 丹麦语
    value: da
  - label:
      en_US: Dutch
      zh_Hans: 荷兰语
    value: nl
  - label:
      en_US: English
      zh_Hans: 英语
    value: en
  - label:
      en_US: Estonian
      zh_Hans: 爱沙尼亚语
    value: et
  - label:
      en_US: Finnish
      zh_Hans: 芬兰语
    value: fi
  - label:
      en_US: French
      zh_Hans: 法语
    value: fr
  - label:
      en_US: German
      zh_Hans: 德语
    value: de
  - label:
      en_US: Greek
      zh_Hans: 希腊语
    value: el
  - label:
      en_US: Hebrew
      zh_Hans: 希伯来语
    value: iw
  - label:
      en_US: Hindi
      zh_Hans: 印地语
    value: hi
  - label:
      en_US: Hungarian
      zh_Hans: 匈牙利语
    value: hu
  - label:
      en_US: Indonesian
      zh_Hans: 印尼语
    value: id
  - label:
      en_US: Italian
      zh_Hans: 意大利语
    value: it
  - label:
      en_US: Japanese
      zh_Hans: 日语
    value: ja
  - label:
      en_US: Kannada
      zh_Hans: 卡纳达语
    value: kn
  - label:
      en_US: Korean
      zh_Hans: 韩语
    value: ko
  - label:
      en_US: Latvian
      zh_Hans: 拉脱维亚语
    value: lv
  - label:
      en_US: Lithuanian
      zh_Hans: 立陶宛语
    value: lt
  - label:
      en_US: Malay
      zh_Hans: 马来语
    value: my
  - label:
      en_US: Malayalam
      zh_Hans: 马拉雅拉姆语
    value: ml
  - label:
      en_US: Marathi
      zh_Hans: 马拉地语
    value: mr
  - label:
      en_US: Norwegian
      zh_Hans: 挪威语
    value: 'no'
  - label:
      en_US: Polish
      zh_Hans: 波兰语
    value: pl
  - label:
      en_US: Portuguese (Brazil)
      zh_Hans: 葡萄牙语（巴西）
    value: pt-br
  - label:
      en_US: Portuguese (Portugal)
      zh_Hans: 葡萄牙语（葡萄牙）
    value: pt-pt
  - label:
      en_US: Punjabi
      zh_Hans: 旁遮普语
    value: pa
  - label:
      en_US: Romanian
      zh_Hans: 罗马尼亚语
    value: ro
  - label:
      en_US: Russian
      zh_Hans: 俄语
    value: ru
  - label:
      en_US: Serbian
      zh_Hans: 塞尔维亚语
    value: sr
  - label:
      en_US: Slovak
      zh_Hans: 斯洛伐克语
    value: sk
  - label:
      en_US: Slovenian
      zh_Hans: 斯洛文尼亚语
    value: sl
  - label:
      en_US: Spanish
      zh_Hans: 西班牙语
    value: es
  - label:
      en_US: Swedish
      zh_Hans: 瑞典语
    value: sv
  - label:
      en_US: Tamil
      zh_Hans: 泰米尔语
    value: ta
  - label:
      en_US: Telugu
      zh_Hans: 泰卢固语
    value: te
  - label:
      en_US: Thai
      zh_Hans: 泰语
    value: th
  - label:
      en_US: Turkish
      zh_Hans: 土耳其语
    value: tr
  - label:
      en_US: Ukrainian
      zh_Hans: 乌克兰语
    value: uk
  - label:
      en_US: Vietnamese
      zh_Hans: 越南语
    value: vi
  required: false
  type: select
