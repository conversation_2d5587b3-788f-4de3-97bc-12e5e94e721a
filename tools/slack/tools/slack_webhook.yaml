description:
  human:
    en_US: Sending a message on <PERSON>lack via the Incoming Webhook
    pt_BR: Sending a message on Slack via the Incoming Webhook
    zh_Hans: 通过入站 Webhook 在 Slack 上发送消息
  llm: A tool for sending messages to a chat on Slack.
extra:
  python:
    source: tools/slack_webhook.py
identity:
  author: Pan YANG
  icon: icon.svg
  label:
    en_US: Incoming Webhook to send message
    pt_BR: Incoming Webhook to send message
    zh_Hans: 通过入站 Webhook 发送消息
  description:
    en_US: Send a message to a Slack channel using webhook
    zh_Hans: 使用 webhook 发送消息到 Slack 频道
    pt_BR: Enviar uma mensagem para um canal do Slack usando webhook
  name: slack_webhook
parameters:
  - form: form
    human_description:
      en_US: Slack Incoming Webhook url
      pt_BR: Slack Incoming Webhook url
      zh_Hans: Slack 入站 Webhook 的 url
    label:
      en_US: Slack Incoming Webhook url
      pt_BR: Slack Incoming Webhook url
      zh_Hans: Slack 入站 Webhook 的 url
    name: webhook_url
    required: true
    type: string
  - form: llm
    human_description:
      en_US: Content to sent to the channel or person.
      pt_BR: Content to sent to the channel or person.
      zh_Hans: 消息内容文本
    label:
      en_US: content
      pt_BR: content
      zh_Hans: 消息内容
    llm_description: Content of the message
    name: content
    required: true
    type: string
