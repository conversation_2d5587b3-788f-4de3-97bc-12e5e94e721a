credentials_for_provider:
  app_id:
    help:
      en_US: Get your app_id and app_secret from <PERSON><PERSON><PERSON>
      <PERSON>h_<PERSON>: 从飞书获取您的 app_id 和 app_secret
    label:
      en_US: APP ID
    placeholder:
      en_US: Please input your feishu app id
      zh_Hans: 请输入你的飞书 app id
    required: true
    type: text-input
    url: https://open.larkoffice.com/app
  app_secret:
    label:
      en_US: APP Secret
    placeholder:
      en_US: Please input your app secret
      zh_Hans: 请输入你的飞书 app secret
    required: true
    type: secret-input
extra:
  python:
    source: provider/feishu_message.py
identity:
  author: <PERSON> Lea
  description:
    en_US: 'Lark message, requires the following permissions: im:message、im:message.group_msg.

      '
    zh_Hans: '飞书消息，需要开通以下权限: im:message、im:message.group_msg。

      '
  icon: icon.svg
  label:
    en_US: Lark Message
    zh_Hans: 飞书消息
  name: feishu_message
  tags:
  - social
  - productivity
tools:
- tools/send_bot_message.yaml
- tools/get_chat_messages.yaml
- tools/send_webhook_message.yaml
- tools/get_thread_messages.yaml
