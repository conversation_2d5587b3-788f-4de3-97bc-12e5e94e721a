credentials_for_provider:
  azure_openai_api_key:
    help:
      en_US: Please input your Azure OpenAI API key
      pt_BR: Introduza a sua chave de API OpenAI do Azure
      zh_Hans: 请输入你的 Azure OpenAI API key
    label:
      en_US: API key
      pt_BR: API key
      zh_Hans: 密钥
    placeholder:
      en_US: Please input your Azure OpenAI API key
      pt_BR: Introduza a sua chave de API OpenAI do Azure
      zh_Hans: 请输入你的 Azure OpenAI API key
    required: true
    type: secret-input
  azure_openai_api_model_name:
    help:
      en_US: Please input the name of your Azure Openai DALL-E API deployment
      pt_BR: Insira o nome da implantação da API DALL-E do Azure Openai
      zh_Hans: 请输入你的 Azure Openai DALL-E API 部署名称
    label:
      en_US: Deployment Name
      pt_BR: Nome da Implantação
      zh_Hans: 部署名称
    placeholder:
      en_US: Please input the name of your Azure Openai DALL-E API deployment
      pt_BR: Insira o nome da implantação da API DALL-E do Azure Openai
      zh_Hans: 请输入你的 Azure Openai DALL-E API 部署名称
    required: true
    type: text-input
  azure_openai_api_version:
    help:
      en_US: Please input your Azure OpenAI API Version,e.g. 2023-12-01-preview
      pt_BR: Introduza a versão da API OpenAI do Azure,e.g. 2023-12-01-preview
      zh_Hans: 请输入你的 Azure OpenAI API 版本，例如：2023-12-01-preview
    label:
      en_US: API Version
      pt_BR: API Version
      zh_Hans: API 版本
    placeholder:
      en_US: Please input your Azure OpenAI API Version,e.g. 2023-12-01-preview
      pt_BR: Introduza a versão da API OpenAI do Azure,e.g. 2023-12-01-preview
      zh_Hans: 请输入你的 Azure OpenAI API 版本，例如：2023-12-01-preview
    required: true
    type: text-input
  azure_openai_base_url:
    help:
      en_US: Please input your Azure OpenAI Endpoint URL, e.g. https://xxx.openai.azure.com/
      pt_BR: Introduza a URL do Azure OpenAI Endpoint, e.g. https://xxx.openai.azure.com/
      zh_Hans: 请输入你的 Azure OpenAI API域名，例如：https://xxx.openai.azure.com/
    label:
      en_US: API Endpoint URL
      pt_BR: API Endpoint URL
      zh_Hans: API 域名
    placeholder:
      en_US: Please input your Azure OpenAI Endpoint URL, e.g. https://xxx.openai.azure.com/
      pt_BR: Introduza a URL do Azure OpenAI Endpoint, e.g. https://xxx.openai.azure.com/
      zh_Hans: 请输入你的 Azure OpenAI API域名，例如：https://xxx.openai.azure.com/
    required: true
    type: text-input
extra:
  python:
    source: provider/azuredalle.py
identity:
  author: Leslie
  description:
    en_US: Azure DALL-E art
    pt_BR: Azure DALL-E art
    zh_Hans: Azure DALL-E 绘画
  icon: icon.png
  label:
    en_US: Azure DALL-E
    pt_BR: Azure DALL-E
    zh_Hans: Azure DALL-E 绘画
  name: azuredalle
  tags:
  - image
  - productivity
tools:
- tools/dalle3.yaml
