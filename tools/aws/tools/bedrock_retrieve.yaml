identity:
  name: bedrock_retrieve
  author: AWS
  label:
    en_US: Bedrock Retrieve
    zh_Hans: Bedrock检索
    pt_BR: Bedrock Retrieve
  icon: icon.svg

description:
  human:
    en_US: A tool for retrieving relevant information from Amazon Bedrock Knowledge Base. You can find deploy instructions on Github Repo - https://github.com/aws-samples/dify-aws-tool
    zh_Hans: Amazon Bedrock知识库检索工具, 请参考 Github Repo - https://github.com/aws-samples/dify-aws-tool上的部署说明
    pt_BR: A tool for retrieving relevant information from Amazon Bedrock Knowledge Base.
  llm: A tool for retrieving relevant information from Amazon Bedrock Knowledge Base. You can find deploy instructions on Github Repo - https://github.com/aws-samples/dify-aws-tool

parameters:
  - name: aws_region
    type: string
    required: false
    label:
      en_US: AWS Region
      zh_Hans: AWS区域
    human_description:
      en_US: AWS region for the Bedrock service
      zh_Hans: Bedrock服务的AWS区域
    form: form

  - name: aws_access_key_id
    type: string
    required: false
    label:
      en_US: AWS Access Key ID
      zh_Hans: AWS访问密钥ID
    human_description:
      en_US: AWS access key ID for authentication (optional)
      zh_Hans: 用于身份验证的AWS访问密钥ID（可选）
    form: form

  - name: aws_secret_access_key
    type: string
    required: false
    label:
      en_US: AWS Secret Access Key
      zh_Hans: AWS秘密访问密钥
    human_description:
      en_US: AWS secret access key for authentication (optional)
      zh_Hans: 用于身份验证的AWS秘密访问密钥（可选）
    form: form

  - name: result_type
    type: select
    required: true
    label:
      en_US: result type
      zh_Hans: 结果类型
    human_description:
      en_US: return a list of json or texts
      zh_Hans: 返回一个列表，内容是json还是纯文本
    default: text
    options:
      - value: json
        label:
          en_US: JSON
          zh_Hans: JSON
      - value: text
        label:
          en_US: Text
          zh_Hans: 文本
    form: form

  - name: knowledge_base_id
    type: string
    required: true
    label:
      en_US: Bedrock Knowledge Base ID
      zh_Hans: Bedrock知识库ID
      pt_BR: Bedrock Knowledge Base ID
    human_description:
      en_US: ID of the Bedrock Knowledge Base to retrieve from
      zh_Hans: 用于检索的Bedrock知识库ID
      pt_BR: ID of the Bedrock Knowledge Base to retrieve from
    llm_description: ID of the Bedrock Knowledge Base to retrieve from
    form: form

  - name: query
    type: string
    required: true
    label:
      en_US: Query string
      zh_Hans: 查询语句
      pt_BR: Query string
    human_description:
      en_US: The search query to retrieve relevant information
      zh_Hans: 用于检索相关信息的查询语句
      pt_BR: The search query to retrieve relevant information
    llm_description: The search query to retrieve relevant information
    form: llm

  - name: topk
    type: number
    required: false
    form: form
    label:
      en_US: Limit for results count
      zh_Hans: 返回结果数量限制
      pt_BR: Limit for results count
    human_description:
      en_US: Maximum number of results to return
      zh_Hans: 最大返回结果数量
      pt_BR: Maximum number of results to return
    min: 1
    max: 10
    default: 5

  - name: search_type
    type: select
    required: false
    label:
      en_US: search type
      zh_Hans: 搜索类型
      pt_BR: search type
    human_description:
      en_US: search type
      zh_Hans: 搜索类型
      pt_BR: search type
    llm_description: search type
    default: SEMANTIC
    options:
      - value: SEMANTIC
        label:
          en_US: SEMANTIC
          zh_Hans: 语义搜索
      - value: HYBRID
        label:
          en_US: HYBRID
          zh_Hans: 混合搜索
    form: form

  - name: rerank_model_id
    type: select
    required: false
    label:
      en_US: rerank model id
      zh_Hans: 重拍模型ID
      pt_BR: rerank model id
    human_description:
      en_US: rerank model id
      zh_Hans: 重拍模型ID
      pt_BR: rerank model id
    llm_description: rerank model id
    default: default
    options:
      - value: default
        label:
          en_US: default
          zh_Hans: 默认
      - value: cohere.rerank-v3-5:0
        label:
          en_US: cohere.rerank-v3-5:0
          zh_Hans: cohere.rerank-v3-5:0
      - value: amazon.rerank-v1:0
        label:
          en_US: amazon.rerank-v1:0
          zh_Hans: amazon.rerank-v1:0
    form: form

  - name: metadata_filter   # Additional parameter for metadata filtering
    type: string            # String type, expects JSON-formatted filter conditions
    required: false         # Optional field - can be omitted
    label:
      en_US: Metadata Filter
      zh_Hans: 元数据过滤器
      pt_BR: Metadata Filter
    human_description:
      en_US: 'JSON formatted filter conditions for metadata (e.g., {"greaterThan": {"key: "aaa", "value": 10}})'
      zh_Hans: '元数据的JSON格式过滤条件（例如，{{"greaterThan": {"key: "aaa", "value": 10}}）'
      pt_BR: 'JSON formatted filter conditions for metadata (e.g., {"greaterThan": {"key: "aaa", "value": 10}})'
    form: form
extra:
  python:
    source: tools/bedrock_retrieve.py