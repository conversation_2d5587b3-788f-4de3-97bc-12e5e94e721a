author: lang<PERSON><PERSON>
created_at: "2024-09-20T08:03:44.658609186Z"
description:
  en_US:
    SearchApi is a robust real-time SERP API delivering structured data from
    a collection of search engines including Google Search, Google Jobs, YouTube,
    Google News, and many more.
  pt_BR:
    SearchApi is a robust real-time SERP API delivering structured data from
    a collection of search engines including Google Search, Google Jobs, YouTube,
    Google News, and many more.
  zh_Hans: SearchApi 是一个强大的实时 SERP API，可提供来自 Google 搜索、Google 招聘、YouTube、Google 新闻等搜索引擎集合的结构化数据。
icon: icon.svg
label:
  en_US: SearchApi
  pt_BR: SearchApi
  zh_Hans: SearchApi
meta:
  arch:
    - amd64
    - arm64
  runner:
    entrypoint: main
    language: python
    version: "3.12"
  version: 0.0.1
name: searchapi
plugins:
  tools:
    - provider/searchapi.yaml
resource:
  memory: 1048576
  permission:
    model:
      enabled: true
      llm: true
    tool:
      enabled: true
tags:
  - search
  - business
  - news
  - productivity
type: plugin
version: 0.0.2
