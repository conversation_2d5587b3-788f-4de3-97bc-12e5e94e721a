description:
  human:
    en_US: Add Members
    zh_Hans: 添加任务成员
  llm: A tool for adding members to a Feishu task.(添加任务成员)
extra:
  python:
    source: tools/add_members.py
identity:
  author: <PERSON>
  label:
    en_US: Add Members
    zh_Hans: 添加任务成员
  name: add_members
parameters:
- form: llm
  human_description:
    en_US: 'The GUID of the task to be added, supports passing either the Task ID
      or the Task link URL. Example of Task ID: 8b5425ec-9f2a-43bd-a3ab-01912f50282b;
      Example of Task link URL: https://applink.feishu-pre.net/client/todo/detail?guid=8c6bf822-e4da-449a-b82a-dc44020f9be9&suite_entity_num=t21587362

      '
    zh_Hans: 要添加的任务的 GUID，支持传任务 ID 和任务链接 URL。任务 ID 示例：8b5425ec-9f2a-43bd-a3ab-01912f50282b；任务链接
      URL 示例：https://applink.feishu-pre.net/client/todo/detail?guid=8c6bf822-e4da-449a-b82a-dc44020f9be9&suite_entity_num=t21587362
  label:
    en_US: Task GUID
    zh_Hans: 任务 GUID
  llm_description: 要添加的任务的 GUID，支持传任务 ID 和任务链接 URL。任务 ID 示例：8b5425ec-9f2a-43bd-a3ab-01912f50282b；任务链接
    URL 示例：https://applink.feishu-pre.net/client/todo/detail?guid=8c6bf822-e4da-449a-b82a-dc44020f9be9&suite_entity_num=t21587362
  name: task_guid
  required: true
  type: string
- form: llm
  human_description:
    en_US: A list of member emails or phone numbers, separated by commas.
    zh_Hans: 任务成员邮箱或者手机号列表，使用逗号分隔。
  label:
    en_US: Task Member Phone Or Email
    zh_Hans: 任务成员的电话或邮箱
  llm_description: 任务成员邮箱或者手机号列表，使用逗号分隔。
  name: member_phone_or_email
  required: true
  type: string
- default: follower
  form: form
  human_description:
    en_US: Member role, optional values are "assignee" (responsible person) and "follower"
      (observer), with a default value of "assignee".
    zh_Hans: 成员的角色，可选值有 "assignee"（负责人）和 "follower"（关注人），默认值为 "assignee"。
  label:
    en_US: member_role
    zh_Hans: 成员的角色
  llm_description: 成员的角色，可选值有 "assignee"（负责人）和 "follower"（关注人），默认值为 "assignee"。
  name: member_role
  options:
  - label:
      en_US: assignee
      zh_Hans: 负责人
    value: assignee
  - label:
      en_US: follower
      zh_Hans: 关注人
    value: follower
  required: true
  type: select
