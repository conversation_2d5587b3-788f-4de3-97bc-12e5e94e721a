description:
  human:
    en_US: List records from the worksheet
    zh_Hans: 查询工作表的记录列表数据，一次最多1000行，可分页获取
  llm: A tool to retrieve record data from the specific worksheet.
extra:
  python:
    source: tools/list_worksheet_records.py
identity:
  author: <PERSON><PERSON><PERSON><PERSON>
  label:
    en_US: List Worksheet Records
    zh_Hans: 查询工作表记录数据
  name: list_worksheet_records
parameters:
- form: form
  human_description:
    en_US: The AppKey parameter for the HAP application, typically found in the application's
      API documentation.
    zh_Hans: HAP 应用的 AppKey 参数，可以从应用 API 文档中查找到
  label:
    en_US: App Key
    zh_Hans: App Key
  llm_description: the AppKey parameter for the HAP application
  name: appkey
  required: true
  type: secret-input
- form: form
  human_description:
    en_US: The Sign parameter for the HAP application
    zh_Hans: HAP 应用的 Sign 参数
  label:
    en_US: Sign
    zh_Hans: Sign
  llm_description: the Sign parameter for the HAP application
  name: sign
  required: true
  type: secret-input
- form: llm
  human_description:
    en_US: The ID of the worksheet from which to retrieve record data
    zh_Hans: 要获取记录数据的工作表 ID
  label:
    en_US: Worksheet ID
    zh_Hans: 工作表 ID
  llm_description: This parameter specifies the ID of the worksheet where the records
    are stored.
  name: worksheet_id
  required: true
  type: string
- form: llm
  human_description:
    en_US: A comma-separated list of field IDs whose data to retrieve. If not provided,
      all fields' data will be fetched
    zh_Hans: 要获取记录数据的字段 ID，多个 ID 间用英文逗号隔开，不传此参数则将获取所有字段的数据
  label:
    en_US: Field IDs
    zh_Hans: 字段 ID 列表
  llm_description: This optional parameter lets you specify a comma-separated list
    of field IDs. Unless the user explicitly requests to output the specified field
    in the question, this parameter should usually be omitted. If this parameter is
    omitted, the API will return data for all fields by default. When provided, only
    the data associated with these fields will be included in the response.
  name: field_ids
  required: false
  type: string
- form: llm
  human_description:
    en_US: A combination of filters applied to query records, formatted as a JSON
      array. See the application's API documentation for details on its structure
      and usage.
    zh_Hans: 查询记录的筛选条件组合，格式为 JSON 数组，可以从应用 API 文档中了解参数结构详情
  label:
    en_US: Filter Set
    zh_Hans: 筛选器组合
  llm_description: "This parameter allows you to specify a set of conditions that\
    \ records must meet to be included in the result set. It is formatted as a JSON\
    \ array, with its structure defined as follows:\n```\ntype Filters = { // filter\
    \ object array\n  controlId: string; // fieldId\n  dataType: number; // fieldTypeId\n\
    \  spliceType: number; // condition concatenation method, 1: And, 2: Or\n  filterType:\
    \ number; // expression type, refer to the <FilterTypeEnum Reference> for enumerable\
    \ values\n  values?: string[]; // values in the condition, for option-type fields,\
    \ multiple values can be passed\n  value?: string; // value in the condition,\
    \ a single value can be passed according to the field type\n  dateRange?: number;\
    \ // date range, mandatory when filterType is 17 or 18, refer to the <DateRangeEnum\
    \ Reference> for enumerable values\n  minValue?: string; // minimum value for\
    \ custom range\n  maxValue?: string; // maximum value for custom range\n  isAsc?:\
    \ boolean; // ascending order, false: descending, true: ascending\n}[];\n```\n\
    For option-type fields, if this option field has `options`, then you need to get\
    \ the corresponding `key` value from the `options` in the current field information\
    \ via `value`, and pass it into `values` in array format. Do not use the `options`\
    \ value of other fields as input conditions.\n\n### FilterTypeEnum Reference\n\
    ```\nEnum Value, Enum Character, Description\n1, Like, Contains(Include)\n2, Eq,\
    \ Is (Equal)\n3, Start, Starts With\n4, End, Ends With\n5, NotLike, Does Not Contain(Not\
    \ Include)\n6, Ne, Is Not (Not Equal)\n7, IsEmpty, Empty\n8, HasValue, Not Empty\n\
    11, Between, Within Range(Belong to)\n12, NotBetween, Outside Range(Not belong\
    \ to)\n13, Gt, Greater Than\n14, Gte, Greater Than or Equal To\n15, Lt, Less Than\n\
    16, Lte, Less Than or Equal To\n17, DateEnum, Date Is\n18, NotDateEnum, Date Is\
    \ Not\n24, RCEq, Associated Field Is\n25, RCNe, Associated Field Is Not\n26, ArrEq,\
    \ Array Equals\n27, ArrNe, Array Does Not Equal\n31, DateBetween, Date Within\
    \ Range (can only be used with minValue and maxValue)\n32, DateNotBetween, Date\
    \ Not Within Range (can only be used with minValue and maxValue)\n33, DateGt,\
    \ Date Later Than\n34, DateGte, Date Later Than or Equal To\n35, DateLt, Date\
    \ Earlier Than\n36, DateLte, Date Earlier Than or Equal To\n```\n\n### DateRangeEnum\
    \ Reference\n```\nEnum Value, Enum Character, Description\n1, Today, Today\n2,\
    \ Yesterday, Yesterday\n3, Tomorrow, Tomorrow\n4, ThisWeek, This Week\n5, LastWeek,\
    \ Last Week\n6, NextWeek, Next Week\n7, ThisMonth, This Month\n8, LastMonth, Last\
    \ Month\n9, NextMonth, Next Month\n12, ThisQuarter, This Quarter\n13, LastQuarter,\
    \ Last Quarter\n14, NextQuarter, Next Quarter\n15, ThisYear, This Year\n16, LastYear,\
    \ Last Year\n17, NextYear, Next Year\n18, Customize, Custom\n21, Last7Day, Past\
    \ 7 Days\n22, Last14Day, Past 14 Days\n23, Last30Day, Past 30 Days\n31, Next7Day,\
    \ Next 7 Days\n32, Next14Day, Next 14 Days\n33, Next33Day, Next 33 Days\n```\n"
  name: filters
  required: false
  type: string
- form: llm
  human_description:
    en_US: The ID of the field used for sorting
    zh_Hans: 用以排序的字段 ID
  label:
    en_US: Sort Field ID
    zh_Hans: 排序字段 ID
  llm_description: This optional parameter specifies the unique identifier of the
    field that will be used to sort the results. It should be set to the ID of an
    existing field within your data structure.
  name: sort_id
  required: false
  type: string
- form: llm
  human_description:
    en_US: Determines whether the sorting is in ascending (true) or descending (false)
      order
    zh_Hans: 排序字段的排序方式：true-升序，false-降序
  label:
    en_US: Ascending Order
    zh_Hans: 是否升序排列
  llm_description: This optional parameter controls the direction of the sort. If
    set to true, the results will be sorted in ascending order; if false, they will
    be sorted in descending order.
  name: sort_is_asc
  required: false
  type: boolean
- form: llm
  human_description:
    en_US: The maximum number of records to retrieve
    zh_Hans: 要获取的记录数量限制条数
  label:
    en_US: Record Limit
    zh_Hans: 记录数量限制
  llm_description: This optional parameter allows you to specify the maximum number
    of records that should be returned in the result set. When retrieving paginated
    record data, this parameter indicates the number of rows to fetch per page, and
    must be used in conjunction with the `page_index` parameter.
  name: limit
  required: false
  type: number
- form: llm
  human_description:
    en_US: The page number when paginating through a list of records
    zh_Hans: 分页读取记录列表时的页码
  label:
    en_US: Page Index
    zh_Hans: 页码
  llm_description: This parameter is used when you need to paginate through a large
    set of records. The default value is 1, which refers to the first page. When it
    is used, the meaning of the `limit` parameter becomes the number of records per
    page.
  name: page_index
  required: false
  type: number
- form: form
  human_description:
    en_US: The address for the privately deployed HAP server.
    zh_Hans: 私有部署 HAP 服务器地址，公有云无需填写
  label:
    en_US: Host Address
    zh_Hans: 服务器地址
  llm_description: the address for the privately deployed HAP server.
  name: host
  required: false
  type: string
- default: table
  form: form
  human_description:
    en_US: used for selecting the result type, table styled text or json text
    zh_Hans: 用于选择结果类型，使用表格格式文本还是JSON格式文本
  label:
    en_US: Result type
    zh_Hans: 结果类型
  name: result_type
  options:
  - label:
      en_US: table text
      zh_Hans: 表格文本
    value: table
  - label:
      en_US: json text
      zh_Hans: JSON文本
    value: json
  required: true
  type: select
