description:
  human:
    en_US: A tool for get analytics about a ticker from Yahoo Finance.
    pt_BR: Uma ferramenta para obter análises sobre um ticker do Yahoo Finance.
    zh_Hans: 一个用于从雅虎财经获取分析数据的工具。
  llm: A tool for get analytics from Yahoo Finance. Input should be the ticker symbol
    like AAPL.
extra:
  python:
    source: tools/analytics.py
identity:
  author: langgenius
  icon: icon.svg
  label:
    en_US: Analytics
    pt_BR: Análises
    zh_Hans: 分析
  name: yahoo_finance_analytics
parameters:
- form: llm
  human_description:
    en_US: The ticker symbol of the company you want to analyze.
    pt_BR: O símbolo do ticker da empresa que você deseja analisar.
    zh_Hans: 你想要搜索的公司的股票代码。
  label:
    en_US: Ticker symbol
    pt_BR: Símbolo do ticker
    zh_Hans: 股票代码
  llm_description: The ticker symbol of the company you want to analyze.
  name: symbol
  required: true
  type: string
- form: llm
  human_description:
    en_US: The start date of the analytics.
    pt_BR: A data de início das análises.
    zh_Hans: 分析的开始日期。
  label:
    en_US: Start date
    pt_BR: Data de início
    zh_Hans: 开始日期
  llm_description: The start date of the analytics, the format of the date must be
    YYYY-MM-DD like 2020-01-01.
  name: start_date
  required: false
  type: string
- form: llm
  human_description:
    en_US: The end date of the analytics.
    pt_BR: A data de término das análises.
    zh_Hans: 分析的结束日期。
  label:
    en_US: End date
    pt_BR: Data de término
    zh_Hans: 结束日期
  llm_description: The end date of the analytics, the format of the date must be YYYY-MM-DD
    like 2024-01-01.
  name: end_date
  required: false
  type: string
