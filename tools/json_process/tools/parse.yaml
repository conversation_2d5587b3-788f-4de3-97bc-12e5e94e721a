description:
  human:
    en_US: A tool for extracting JSON objects
    pt_BR: A tool for extracting JSON objects
    zh_Hans: 一个解析JSON对象的工具
  llm: A tool for extracting JSON objects
extra:
  python:
    source: tools/parse.py
identity:
  author: <PERSON><PERSON>_<PERSON>
  label:
    en_US: JSON Parse
    pt_BR: JSON Parse
    zh_Hans: JSON 解析
  name: parse
parameters:
- form: llm
  human_description:
    en_US: JSON data
    pt_BR: JSON数据
    zh_Hans: JSON数据
  label:
    en_US: JSON data
    pt_BR: JSON data
    zh_Hans: JSON数据
  llm_description: JSON data to be processed
  name: content
  required: true
  type: string
- form: llm
  human_description:
    en_US: JSON fields to be parsed
    pt_BR: JSON fields to be parsed
    zh_Hans: 需要解析的 JSON 字段
  label:
    en_US: JSON filter
    pt_BR: JSON filter
    zh_Hans: JSON解析对象
  llm_description: JSON fields to be parsed
  name: json_filter
  required: true
  type: string
- default: true
  form: form
  human_description:
    en_US: Ensure the JSON output is ASCII encoded
    pt_BR: Ensure the JSON output is ASCII encoded
    zh_Hans: 确保输出的 JSON 是 ASCII 编码
  label:
    en_US: Ensure ASCII
    pt_BR: Ensure ASCII
    zh_Hans: 确保 ASCII
  name: ensure_ascii
  type: boolean
