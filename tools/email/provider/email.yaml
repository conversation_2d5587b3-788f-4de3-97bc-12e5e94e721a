credentials_for_provider:
  email_account:
    help:
      en_US: email account
      zh_Hans: 邮件账号
    label:
      en_US: email account
      zh_Hans: 邮件账号
    placeholder:
      en_US: input you email account
      zh_Hans: 输入你的邮箱账号
    required: true
    type: text-input
  email_password:
    help:
      en_US: email password
      zh_Hans: 邮件密码
    label:
      en_US: email password
      zh_Hans: 邮件密码
    placeholder:
      en_US: email password
      zh_Hans: 邮件密码
    required: true
    type: secret-input
  encrypt_method:
    help:
      en_US: smtp server encrypt method
      zh_Hans: 发信smtp服务器加密方式
    label:
      en_US: encrypt method
      zh_Hans: 加密方式
    options:
    - label:
        en_US: NONE
        zh_Hans: 无加密
      value: NONE
    - label:
        en_US: SSL
        zh_Hans: SSL加密
      value: SSL
    - label:
        en_US: START TLS
        zh_Hans: START TLS加密
      value: TLS
    required: true
    type: select
  smtp_port:
    help:
      en_US: smtp server port
      zh_Hans: 发信smtp服务器端口
    label:
      en_US: smtp server port
      zh_Hans: 发信smtp服务器端口
    placeholder:
      en_US: smtp server port
      zh_Hans: 发信smtp服务器端口
    required: true
    type: text-input
  smtp_server:
    help:
      en_US: smtp server
      zh_Hans: 发信smtp服务器地址
    label:
      en_US: smtp server
      zh_Hans: 发信smtp服务器地址
    placeholder:
      en_US: smtp server
      zh_Hans: 发信smtp服务器地址
    required: true
    type: text-input
  sender_address:
    help:
      en_US: The designated sender address if different to the `email_account`. This may be required for using something like AWS's Simple EMail Service
      zh_Hans: 指定的发件人地址与 `email_account` 不同。使用类似 AWS 的 Simple EMail Service 时可能需要此设置。
    label:
      en_US: Sender Address
      zh_Hans: 发件人地址
    placeholder:
      en_US: sender address
      zh_Hans: 发件人地址
    required: false
    type: text-input
extra:
  python:
    source: provider/email.py
identity:
  author: wakaka6
  description:
    en_US: send email through smtp protocol
    zh_Hans: 通过smtp协议发送电子邮件
  icon: icon.svg
  label:
    en_US: email
    zh_Hans: 电子邮件
  name: email
  tags:
    - utilities
tools:
  - tools/send_mail_batch.yaml
  - tools/send_mail.yaml
