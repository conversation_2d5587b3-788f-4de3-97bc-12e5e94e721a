description:
  human:
    en_US: A tool for sending email
    zh_Hans: 用于发送邮件
  llm: A tool for sending email
extra:
  python:
    source: tools/send_mail.py
identity:
  author: wakaka6
  icon: icon.svg
  label:
    en_US: send email
    zh_Hans: 发送邮件
  name: send_mail
parameters:
  - form: llm
    human_description:
      en_US: Recipient email account
      zh_Hans: 收件人邮箱账号
    label:
      en_US: Recipient email account
      zh_Hans: 收件人邮箱账号
    llm_description: Recipient email account
    name: send_to
    required: true
    type: string
  - form: llm
    human_description:
      en_US: Reply-to email address
      zh_Hans: 回复电子邮件地址
    label:
      en_US: Reply-to email address
      zh_Hans: 回复电子邮件地址
    llm_description: Reply-to email address
    name: reply_to
    required: false
    type: string
  - form: llm
    human_description:
      en_US: Carbon copy email account
      zh_Hans: 抄送邮箱账号
    label:
      en_US: Carbon copy email account(json list)
      zh_Hans: 抄送邮箱账号(json list)
    llm_description: Carbon copy email account(json list)
    name: cc
    required: false
    type: string
  - form: llm
    human_description:
      en_US: Blind carbon copy email account
      zh_Hans: 密送邮箱账号
    label:
      en_US: Blind carbon copy email account(json list)
      zh_Hans: 密送邮箱账号(json list)
    llm_description: Blind carbon copy email account(json list)
    name: bcc
    required: false
    type: string
  - form: llm
    human_description:
      en_US: email subject
      zh_Hans: 邮件主题
    label:
      en_US: email subject
      zh_Hans: 邮件主题
    llm_description: email subject
    name: subject
    required: true
    type: string
  - form: llm
    human_description:
      en_US: email content
      zh_Hans: 邮件内容
    label:
      en_US: email content
      zh_Hans: 邮件内容
    llm_description: email content
    name: email_content
    required: true
    type: string
  - form: form
    human_description:
      en_US: Convert content from markdown to HTML
      zh_Hans: 将内容从Markdown转换为HTML
    label:
      en_US: Convert to HTML
      zh_Hans: 转换为HTML
    llm_description: Whether to convert the email content from markdown to HTML format
    name: convert_to_html
    required: false
    type: boolean
    default: false
  - form: llm
    human_description:
      en_US: Files to attach to the email
      zh_Hans: 要附加到电子邮件的文件
    label:
      en_US: File Attachments
      zh_Hans: 文件附件
    llm_description: Files to attach to the email
    name: attachments
    required: false
    type: files
    array: true

