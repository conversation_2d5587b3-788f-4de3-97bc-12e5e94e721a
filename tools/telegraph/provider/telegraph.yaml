identity:
  author: langgenius
  name: telegraph
  label:
    en_US: Telegraph
    zh_Hans: Telegraph
    pt_BR: Telegraph
  tags:
    - productivity
    - utilities
  description:
    en_US: A Telegraph plugin that allows you to publish your content easily
    ja_JP: コンテンツを簡単に公開できるTelegraphプラグイン
    zh_Hans: 一个让您轻松发布内容的Telegraph插件
    pt_BR: Um plugin do Telegraph que permite publicar seu conteúdo facilmente
  icon: icon.jpg
credentials_for_provider:
  telegraph_access_token:
    type: secret-input
    required: true
    label:
      en_US: Telegraph Access Token
      ja_JP: Telegraphアクセストークン
      zh_Hans: Telegraph 访问令牌
      pt_BR: Token de Acesso do Telegraph
    placeholder:
      en_US: Enter your Telegraph access token
      ja_JP: Telegraphアクセストークンを入力してください
      zh_Hans: 请输入您的 Telegraph 访问令牌
      pt_BR: Digite seu token de acesso do Telegraph
    help:
      en_US: How to get your Telegraph access token
      ja_JP: Telegraphアクセストークンの取得方法
      zh_Hans: 如何获取 Telegraph 访问令牌
      pt_BR: Como obter seu token de acesso do Telegraph
    url: https://telegra.ph/api#createAccount
tools:
  - tools/telegraph.yaml
  - tools/telegraph_advanced.yaml
extra:
  python:
    source: provider/telegraph.py
