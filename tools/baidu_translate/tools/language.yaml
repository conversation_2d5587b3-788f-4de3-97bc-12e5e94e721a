description:
  human:
    en_US: A tool for Baidu Language, support Chinese, English, Japanese, Korean,
      Thai, Vietnamese and Russian
    zh_Hans: 使用百度进行语种识别，支持的语种：中文、英语、日语、韩语、泰语、越南语和俄语
  llm: A tool for Baidu Language
extra:
  python:
    source: tools/language.py
identity:
  author: <PERSON>
  label:
    en_US: Baidu Language
    zh_Hans: 百度语种识别
  name: language
parameters:
- form: llm
  human_description:
    en_US: Text content to be recognized
    zh_Hans: 需要识别语言的文本内容
  label:
    en_US: Text content
    zh_Hans: 文本内容
  llm_description: Text content to be recognized
  name: q
  required: true
  type: string
- default: Chinese
  form: form
  human_description:
    en_US: Describe the language used to identify the results
    zh_Hans: 描述识别结果所用的语言
  label:
    en_US: Description language
    zh_Hans: 描述语言
  name: description_language
  options:
  - label:
      en_US: Chinese
      zh_Hans: 中文
    value: Chinese
  - label:
      en_US: English
      zh_Hans: 英语
    value: English
  required: true
  type: select
