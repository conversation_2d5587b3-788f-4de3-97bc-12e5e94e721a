description:
  human:
    en_US: step-1x is a text to image tool
    pt_BR: step-1x is a text to image tool
    zh_Hans: step-1x 是一个文本/图像到图像的工具
  llm: step-1x is a tool used to generate images from text or image
extra:
  python:
    source: tools/image.py
identity:
  author: Stepfun
  description:
    en_US: step-1x is a powerful drawing tool by stepfun, you can draw the image based
      on your prompt
    pt_BR: step-1x is a powerful drawing tool by stepfun, you can draw the image based
      on your prompt
    zh_Hans: step-1x 系列是阶跃星辰提供的强大的绘画工具，它可以根据您的提示词绘制出您想要的图像。
  label:
    en_US: step-1x
    pt_BR: step-1x
    zh_Hans: 阶跃星辰绘画
  name: stepfun
parameters:
- form: llm
  human_description:
    en_US: Image prompt, you can check the official documentation of step-1x
    pt_BR: Image prompt, you can check the official documentation of step-1x
    zh_Hans: 图像提示词，您可以查看 step-1x 的官方文档
  label:
    en_US: Prompt
    pt_BR: Prompt
    zh_Hans: 提示词
  llm_description: Image prompt of step-1x you should describe the image you want
    to generate as a list of words as possible as detailed
  name: prompt
  required: true
  type: string
- default: 1024x1024
  form: form
  human_description:
    en_US: The size of the generated image
    pt_BR: The size of the generated image
    zh_Hans: 生成的图片大小
  label:
    en_US: Image size
    pt_BR: Image size
    zh_Hans: 图像大小
  name: size
  options:
  - label:
      en_US: 256x256
      pt_BR: 256x256
      zh_Hans: 256x256
    value: 256x256
  - label:
      en_US: 512x512
      pt_BR: 512x512
      zh_Hans: 512x512
    value: 512x512
  - label:
      en_US: 768x768
      pt_BR: 768x768
      zh_Hans: 768x768
    value: 768x768
  - label:
      en_US: 1024x1024
      pt_BR: 1024x1024
      zh_Hans: 1024x1024
    value: 1024x1024
  - label:
      en_US: 1280x800
      pt_BR: 1280x800
      zh_Hans: 1280x800
    value: 1280x800
  - label:
      en_US: 800x1280
      pt_BR: 800x1280
      zh_Hans: 800x1280
    value: 800x1280
  required: false
  type: select
- default: 1
  form: form
  human_description:
    en_US: Number of generated images, now only one image can be generated at a time
    pt_BR: Number of generated images, now only one image can be generated at a time
    zh_Hans: 生成的图像数量，当前仅支持每次生成一张图片
  label:
    en_US: Number of generated images
    pt_BR: Number of generated images
    zh_Hans: 生成的图像数量
  max: 1
  min: 1
  name: n
  required: true
  type: number
- default: 10
  form: form
  human_description:
    en_US: seed
    pt_BR: seed
    zh_Hans: seed
  label:
    en_US: seed
    pt_BR: seed
    zh_Hans: seed
  name: seed
  required: false
  type: number
- default: 50
  form: form
  human_description:
    en_US: Steps, now support integers between 1 and 100
    pt_BR: Steps, now support integers between 1 and 100
    zh_Hans: Steps, 当前支持 1～100 之间整数
  label:
    en_US: Steps
    pt_BR: Steps
    zh_Hans: Steps
  max: 100
  min: 1
  name: steps
  required: false
  type: number
- default: 7.5
  form: form
  human_description:
    en_US: classifier-free guidance scale
    pt_BR: classifier-free guidance scale
    zh_Hans: classifier-free guidance scale
  label:
    en_US: classifier-free guidance scale
    pt_BR: classifier-free guidance scale
    zh_Hans: classifier-free guidance scale
  max: 10
  min: 1
  name: cfg_scale
  required: false
  type: number
