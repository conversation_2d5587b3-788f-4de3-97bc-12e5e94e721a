description:
  human:
    en_US: FLUX.1 [pro] new is an accelerated version of FLUX.1 [pro], maintaining
      professional-grade image quality while delivering significantly faster generation
      speeds.
    zh_Hans: FLUX.1 [pro] new 是 FLUX.1 [pro] 的加速版本，在保持专业级图像质量的同时，大大提高了生成速度。
  llm: This tool generates images from prompts using FAL's FLUX.1 [pro] new model.
extra:
  python:
    source: tools/flux_1_pro_new.py
identity:
  author: <PERSON><PERSON>
  icon: icon.svg
  label:
    en_US: FLUX.1 [pro] new
    zh_Hans: FLUX.1 [pro] new
  name: flux_1_pro_new
parameters:
- form: llm
  human_description:
    en_US: The text prompt used to generate the image.
    zh_Hans: 用于生成图像的文本提示。
  label:
    en_US: Prompt
    zh_Hans: 提示词
  llm_description: This prompt text will be used to generate the image.
  name: prompt
  required: true
  type: string
- default: landscape_4_3
  form: form
  human_description:
    en_US: The size of the generated image.
    zh_Hans: 生成图像的尺寸。
  label:
    en_US: Image Size
    zh_Hans: 图像尺寸
  name: image_size
  options:
  - label:
      en_US: Square HD
      zh_Hans: 正方形高清
    value: square_hd
  - label:
      en_US: Square
      zh_Hans: 正方形
    value: square
  - label:
      en_US: Portrait 4:3
      zh_Hans: 竖屏 4:3
    value: portrait_4_3
  - label:
      en_US: Portrait 16:9
      zh_Hans: 竖屏 16:9
    value: portrait_16_9
  - label:
      en_US: Landscape 4:3
      zh_Hans: 横屏 4:3
    value: landscape_4_3
  - label:
      en_US: Landscape 16:9
      zh_Hans: 横屏 16:9
    value: landscape_16_9
  required: false
  type: select
- default: 1
  form: form
  human_description:
    en_US: The number of images to generate.
    zh_Hans: 要生成的图像数量。
  label:
    en_US: Number of Images
    zh_Hans: 图像数量
  max: 1
  min: 1
  name: num_images
  required: false
  type: number
- default: 28
  form: form
  human_description:
    en_US: The number of inference steps to perform. More steps produce higher quality
      but take longer.
    zh_Hans: 执行的推理步数。步数越多，质量越高，但所需时间也更长。
  label:
    en_US: Num Inference Steps
    zh_Hans: 推理步数
  max: 50
  min: 1
  name: num_inference_steps
  required: false
  type: number
- default: 3.5
  form: form
  human_description:
    en_US: How closely the model should follow the prompt.
    zh_Hans: 模型对提示词的遵循程度。
  label:
    en_US: Guidance Scale
    zh_Hans: 指导强度
  max: 20
  min: 0
  name: guidance_scale
  required: false
  type: number
- default: '2'
  form: form
  human_description:
    en_US: 'The safety tolerance level for the generated image. 1 being the most strict
      and 5 being the most permissive.

      '
    zh_Hans: '生成图像的安全容忍级别。1 是最严格，6 是最宽松。

      '
  label:
    en_US: Safety Tolerance
    zh_Hans: 安全容忍度
  name: safety_tolerance
  options:
  - label:
      en_US: 1 (Most strict)
      zh_Hans: 1（最严格）
    value: '1'
  - label:
      en_US: '2'
      zh_Hans: '2'
    value: '2'
  - label:
      en_US: '3'
      zh_Hans: '3'
    value: '3'
  - label:
      en_US: '4'
      zh_Hans: '4'
    value: '4'
  - label:
      en_US: '5'
      zh_Hans: '5'
    value: '5'
  - label:
      en_US: 6 (Most permissive)
      zh_Hans: 6（最宽松）
    value: '6'
  required: false
  type: select
- form: form
  human_description:
    en_US: The same seed and prompt can produce similar images.
    zh_Hans: 相同的种子和提示词可以生成相似的图像。
  label:
    en_US: Seed
    zh_Hans: 种子
  max: 9999999999
  min: 0
  name: seed
  required: false
  type: number
- default: false
  form: form
  human_description:
    en_US: 'If set to true, the function will wait for the image to be generated and
      uploaded before returning the response. This will increase the latency but allows
      you to get the image directly in the response without going through the CDN.

      '
    zh_Hans: '如果设置为 true，函数将在生成并上传图像后才返回响应。 这将增加延迟，但允许您直接在响应中获取图像，而无需通过 CDN。

      '
  label:
    en_US: Sync Mode
    zh_Hans: 同步模式
  name: sync_mode
  required: false
  type: boolean
