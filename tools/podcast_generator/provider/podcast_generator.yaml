credentials_for_provider:
  api_key:
    label:
      en_US: API Key
      zh_Hans: API 密钥
    placeholder:
      en_US: Enter your TTS service API key
      zh_Hans: 输入您的 TTS 服务 API 密钥
    required: true
    type: secret-input
  openai_base_url:
    help:
      en_US: Please input your OpenAI base URL
      zh_Hans: 请输入你的 OpenAI base URL
    label:
      en_US: OpenAI base URL
      zh_Hans: OpenAI base URL
    placeholder:
      en_US: Please input your OpenAI base URL
      zh_Hans: 请输入你的 OpenAI base URL
    required: false
    type: text-input
  tts_service:
    label:
      en_US: TTS Service
      zh_Hans: TTS 服务
    options:
    - label:
        en_US: OpenAI TTS
        zh_Hans: OpenAI TTS
      value: openai
    placeholder:
      en_US: Select a TTS service
      zh_Hans: 选择一个 TTS 服务
    required: true
    type: select
extra:
  python:
    source: provider/podcast_generator.py
identity:
  author: langgenius
  description:
    en_US: Generate podcast audio using Text-to-Speech services
    zh_Hans: 使用文字转语音服务生成播客音频
  icon: icon.svg
  label:
    en_US: Podcast Generator
    zh_Hans: 播客生成器
  name: podcast_generator
tools:
- tools/podcast_audio_generator.yaml
