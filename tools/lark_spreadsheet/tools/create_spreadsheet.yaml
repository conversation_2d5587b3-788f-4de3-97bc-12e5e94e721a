description:
  human:
    en_US: Create Spreadsheet
    zh_Hans: 创建电子表格
  llm: A tool for creating spreadsheets. (创建电子表格)
extra:
  python:
    source: tools/create_spreadsheet.py
identity:
  author: langgenius
  label:
    en_US: Create Spreadsheet
    zh_Hans: 创建电子表格
  name: create_spreadsheet
parameters:
- form: llm
  human_description:
    en_US: The title of the spreadsheet
    zh_Hans: 电子表格的标题
  label:
    en_US: Spreadsheet Title
    zh_Hans: 电子表格标题
  llm_description: 电子表格的标题
  name: title
  required: false
  type: string
- form: llm
  human_description:
    en_US: The token of the folder, supports folder URL input, e.g., https://bytedance.larkoffice.com/drive/folder/CxHEf4DCSlNkL2dUTCJcPRgentg
    zh_Hans: 文件夹 token，支持文件夹 URL 输入，如：https://bytedance.larkoffice.com/drive/folder/CxHEf4DCSlNkL2dUTCJcPRgentg
  label:
    en_US: Folder Token
    zh_Hans: 文件夹 token
  llm_description: 文件夹 token，支持文件夹 URL 输入，如：https://bytedance.larkoffice.com/drive/folder/CxHEf4DCSlNkL2dUTCJcPRgentg
  name: folder_token
  required: false
  type: string
