description:
  human:
    en_US: A tool for searching questions on a Stack Exchange site.
    zh_Hans: 在Stack Exchange站点上搜索问题的工具。
  llm: A tool for searching questions on Stack Exchange site.
extra:
  python:
    source: tools/searchStackExQuestions.py
identity:
  author: <PERSON> Tu
  label:
    en_US: Search Stack Exchange Questions
    zh_Hans: 搜索Stack Exchange问题
  name: searchStackExQuestions
parameters:
- form: llm
  human_description:
    en_US: The search query to use for finding questions.
    zh_Hans: 用于查找问题的搜索查询。
  label:
    en_US: Search query
    zh_Hans: 搜索查询
  llm_description: The search query.
  name: intitle
  required: true
  type: string
- form: llm
  human_description:
    en_US: The sort order for the search results - relevance, activity, votes, or
      creation date.
    zh_Hans: 搜索结果的排序顺序 - 相关性、活动、投票或创建日期。
  label:
    en_US: Sort order
    zh_Hans: 排序
  llm_description: The sort order - 'relevance', 'activity', 'votes', or 'creation'.
  name: sort
  required: true
  type: string
- default: desc
  form: form
  human_description:
    en_US: The direction to sort - ascending or descending.
    zh_Hans: 排序方向 - 升序或降序。
  label:
    en_US: Sort direction
    zh_Hans: 排序方向
  name: order
  options:
  - label:
      en_US: Ascending
      zh_Hans: 升序
    value: asc
  - label:
      en_US: Descending
      zh_Hans: 降序
    value: desc
  required: true
  type: select
- form: llm
  human_description:
    en_US: The Stack Exchange site to search, e.g. stackoverflow, unix, etc.
    zh_Hans: 要搜索的Stack Exchange站点，例如stackoverflow、unix等。
  label:
    en_US: Stack Exchange site
    zh_Hans: Stack Exchange 站点
  llm_description: Stack Exchange site identifier - 'stackoverflow', 'serverfault',
    'superuser', 'askubuntu', 'unix', 'cs', 'softwareengineering', 'codegolf', 'codereview',
    'cstheory', 'security', 'cryptography', 'reverseengineering', 'datascience', 'devops',
    'ux', 'dba', 'gis', 'webmasters', 'arduino', 'raspberrypi', 'networkengineering',
    'iot', 'tor', 'sqa', 'mathoverflow', 'math', 'mathematica', 'dsp', 'gamedev',
    'robotics', 'genai', 'computergraphics'.
  name: site
  required: true
  type: string
- form: llm
  human_description:
    en_US: A semicolon-separated list of tags that questions must have.
    zh_Hans: 问题必须具有的标签的分号分隔列表。
  label:
    en_US: Include tags
    zh_Hans: 包含标签
  llm_description: Semicolon-separated tags to include. Leave blank if not needed.
  name: tagged
  required: false
  type: string
- form: llm
  human_description:
    en_US: A semicolon-separated list of tags to exclude from the search.
    zh_Hans: 从搜索中排除的标签的分号分隔列表。
  label:
    en_US: Exclude tags
    zh_Hans: 排除标签
  llm_description: Semicolon-separated tags to exclude. Leave blank if not needed.
  name: nottagged
  required: false
  type: string
- default: 'true'
  form: form
  human_description:
    en_US: Whether to limit to only questions that have an accepted answer.
    zh_Hans: 是否限制为只有已接受答案的问题。
  label:
    en_US: Has accepted answer
    zh_Hans: 有已接受的答案
  name: accepted
  options:
  - label:
      en_US: 'Yes'
      zh_Hans: 是
    value: 'true'
  - label:
      en_US: 'No'
      zh_Hans: 否
    value: 'false'
  required: true
  type: boolean
- default: 10
  form: form
  human_description:
    en_US: The number of results to return per page.
    zh_Hans: 每页返回的结果数。
  label:
    en_US: Results per page
    zh_Hans: 每页结果数
  llm_description: The number of results per page.
  max: 50
  min: 1
  name: pagesize
  required: true
  type: number
