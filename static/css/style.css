body {
    background-color: #f5f5f5;
    padding: 20px 0;
}

.card {
    margin-top: 20px;
}

.content-box {
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.responsive-audio {
    width: 100%;
    max-width: 500px;
}

#processing-section {
    margin: 30px 0;
}

#status-message {
    text-align: center;
    margin-top: 10px;
    color: #2196F3;
}

.collapsible-body {
    padding: 15px;
}

.audio-info {
    margin-top: 10px;
    color: #757575;
    font-size: 0.9rem;
}

.audio-info p {
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-info i {
    margin-right: 5px;
}

#process-info {
    max-height: none;
}

#process-info p {
    margin: 5px 0;
}

/* 历史任务列表样式 */
#history-section {
    margin-top: 20px;
    margin-bottom: 20px;
}

#task-history-list .collection-item {
    display: flex;
    align-items: center;
}

#task-history-list .grey-text {
    margin-left: 10px;
    font-size: 0.9rem;
}

#progress-text {
    color: #2196F3;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 添加一些响应式调整 */
@media only screen and (max-width: 600px) {
    .container {
        width: 95%;
    }

    .btn {
        width: 100%;
        margin-top: 10px;
    }

    .file-field .btn {
        width: auto;
    }
}
