# 项目业务说明
## 主要爬取15 个公众号和 8 个网站。
- 需求来源： 
    - https://hujialou.quip.com/77NwAtXhV93i/2024-06-13-
- 简要说明：
    - 公众号爬虫
    - 网站爬虫

- 公众号爬虫参考文档：
    - https://github.com/striver-ing/wechat-spider/tree/master/wechat-spider
    - https://github.com/cxyxl66/WeChatCrawler
    - https://github.com/wonderfulsuccess/weixin_crawler/tree/master/weixin_crawler/app/api

- 参考文档：
    - 数据库
      - https://gitee.com/opengauss/openGauss-sqlalchemy
      - https://docs-opengauss.osinfra.cn/zh/docs/5.0.0/docs/GettingStarted/GettingStarted.html
      - https://docs.sqlalchemy.org/en/14/
      - https://github.com/n0nSmoker/SQLAlchemy-serializer
    - 爬虫
      - https://docs.scrapy.org/en/2.11/
    - 定时任务
      - https://docs.celeryq.dev/en/v5.4.0/
      - https://redbeat.readthedocs.io/en/latest/index.html
    - web服务
      - https://fastapi.tiangolo.com/zh/
    - 第三方服务
      - https://www.kuaidaili.com/doc/dev/sdk_http/
      - https://www.kuaidaili.com/doc/dev/sdk_api_proxy/
      - https://dajiala.com/main/history
    - 其他
      - 重试模块 https://tenacity.readthedocs.io/en/latest/
      - 日期时间处理 https://pendulum.eustace.io/docs/

# 项目运行说明
## 基础环境
- python==3.10
- docker 镜像部署
- 使用的其他库参考 [requirements.txt](requirements.txt)
- 由于 使用了 opengauss 作为数据库，sqlalchemy 的版本降级为1.4.52， 2.0+无法安装。
- 依赖的基础服务： OpenGauss、MinIO、Redis

## 爬虫模块只做一件事：
1. 对外提供接口，调起爬虫 job， job结束后，请求回调接口将数据和状态传入。

## docker 构造

参考 [build.sh.sample](build.sh.example)

参考 [docker-compose.yml](docker-compose.yml.example)

## 部署

部署到测试环境的时候，只需要合并到 dev 分支，[gitlab-ci.yml](.gitlab-ci.yml) 会自动完成。

部署脚本参考 [deploy.sh.sample](deploy.wenyin.sh.example)

需要 docker-compose.yml

环境变量配置参考 [.env.sample](.env.example)
