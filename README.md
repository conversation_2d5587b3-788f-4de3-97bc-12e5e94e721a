# CAUC AAC Backend

民航大学 知识图谱项目服务后端

## 开发

### Poetry

建议在虚拟环境下运行项目，然后安装 poetry。
```bash
pip install poetry
```

### Poetry 安装依赖

```bash
# 在项目根目录下执行
# 如果是开发环境，需要安装开发依赖果
poetry install --with dev

# 如果是生产环境，需要安装生产依赖
poetry install --with deploy
```

### Pre-commit

开发的时候需要安装 pre-commit，可以在提交代码前自动检查代码格式。

```bash
pre-commit install
```

### 配置

复制 .env.example 文件到 .env 文件，然后改好里面的内容。

```bash
cp .env.example .env
```

### 本地化（翻译）

参考

https://docs.djangoproject.com/zh-hans/5.1/topics/i18n/translation/#localization-how-to-create-language-files


```shell
# 生成翻译文件，会在 locale/zh_Hans/LC_MESSAGES/ 下生成 django.po 文件，翻译完成后需要编译成 django.mo 文件
python src/manage.py makemessages -l zh_Hans
# 编译翻译文件，注意每次修改 django.po 文件后都需要重新编译，并且文件在 .gitignore 中，不会被提交到仓库
python src/manage.py compilemessages
# locale_override 目录下的是覆盖未被任何代码引用，但来自官方或者第三方库的文本翻译文件，因为 makemessages 会注释掉代码中未使用的文本
# 如果需要修改官方的翻译，可以在这里修改
```

### 下载 spacy 模型

```bash
python -m spacy download zh_core_web_sm
python -m spacy download en_core_web_sm

# 或者可以安装本地模型
# pip install --no-cache-dir prebuilt/zh_core_web_sm-3.8.0-py3-none-any.whl
# pip install --no-cache-dir prebuilt/en_core_web_sm-3.8.0-py3-none-any.whl
```

### 运行
```bash
python src/manage.py runserver
```

## Docker Swarm 部署

### 构建

```bash
docker build --file Dockerfile -t cauc_aac_backend:<镜像版本号> --build-arg VERSION=<构建版本号> .
```

### 配置

复制一份 [.env.example](.env.example) 修改好，并改名到 .env 文件

然后复制 [deploy/docker-compose.yml](deploy/docker-compose.yml)、[deploy/deploy.sh](deploy/deploy.sh) 和 [deploy/nginx.conf](deploy/nginx.conf) 到相同目录

如果有前端项目，放到 frontend 目录下

初始化脚本 .sql 文件放到 init-scripts 目录下

然后运行 `./deploy.sh` 脚本


```shell
# 手动启动

# 至少需要 Docker Engine 20.10.0

export VERSION=<要运行的镜像版本号>

# 读取 .env 文件里面的环境变量
export $(grep -v '^#' .env | xargs)
docker stack deploy -c docker-compose.yml cauc_backend_stack
```
