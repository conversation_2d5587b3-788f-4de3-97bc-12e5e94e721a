import os
from datetime import datetime

from app.models.task import TaskStatus
from app.services.pdf_service import extract_text_from_pdf
from app.services.openai_service import generate_podcast_script
from app.services.audio_service import process_script_to_audio

# 任务状态存储
tasks = {}


async def process_podcast_task(task_id: str, file_path: str, file_name: str):
    """
    处理播客生成任务的主函数
    """
    start_time = datetime.now()
    temp_files = []

    try:
        # 获取当前任务对象
        task = tasks[task_id]

        # 更新任务状态为处理中
        task.status = "processing"
        task.current_stage = "extracting"
        task.updated_at = datetime.now()

        # 记录使用的模型
        text_model = os.getenv("OPENAI_TEXT_MODEL", "gpt-3.5-turbo")
        tts_model = os.getenv("OPENAI_TTS_MODEL", "gpt-4o-mini-tts")
        task.text_model = text_model
        task.tts_model = tts_model

        # 步骤1：提取PDF文本
        text = await extract_text_from_pdf(file_path)
        task.text_content = text
        task.updated_at = datetime.now()

        # 步骤2：生成播客脚本
        task.current_stage = "generating_script"
        task.updated_at = datetime.now()

        # 生成脚本，传入语言参数
        script = await generate_podcast_script(text, task.language)

        # 更新脚本内容并立即保存状态，使前端可以实时获取
        task.script_content = script
        task.updated_at = datetime.now()

        # 步骤3：生成音频
        audio_url, temp_files = await process_script_to_audio(script, task_id, task)
        task.audio_url = audio_url
        task.current_stage = "completed"

        # 计算处理时间
        end_time = datetime.now()
        process_time = (end_time - start_time).total_seconds()
        task.process_time = process_time

        # 更新任务状态为完成
        task.status = "completed"
        task.updated_at = end_time

    except Exception as e:
        # 更新任务状态为失败
        task.status = "failed"
        task.error = str(e)
        task.updated_at = datetime.now()

        # 即使失败也记录处理时间
        end_time = datetime.now()
        process_time = (end_time - start_time).total_seconds()
        task.process_time = process_time

        # 重新抛出异常以便记录
        print(f"任务处理失败: {str(e)}")
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                print(f"删除临时文件失败: {str(e)}")


def get_task(task_id: str) -> TaskStatus:
    """
    获取任务状态
    """
    if task_id not in tasks:
        return None
    return tasks[task_id]


def create_task(task_id: str, file_name: str, language: str = "zh") -> TaskStatus:
    """
    创建新任务
    """
    task = TaskStatus(
        id=task_id,
        status="pending",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        file_name=file_name,
        language=language
    )
    tasks[task_id] = task
    return task
