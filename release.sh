#! /bin/bash

# 进入脚本目录
cd "$(dirname "$0")" || exit 1

# 检查git提交
test -z "$(git status --porcelain)" || { echo "错误：有未提交的更改，请先提交到git。"; exit 1; }

# 创建并推送版本号、更新日志
echo "WARNING: This operation will create version tag and push to gitlab"
if [ -n "$1" ]; then
    RULE=$1
else
  read -r -p "Version Change? (provide the rule major/minor/patch)[patch]: " RULE
fi
RULE=${RULE:-patch}
echo "Version Change Rule: $RULE"
poetry version "$RULE" || { echo "错误：修改版本号失败。"; exit 1; }
VERSION=$(poetry version -s)
export VERSION # gitchangelog 需要此环境变量来获取最新版本号，见.gitchangelog.rc
poetry run gitchangelog > HISTORY.md
git add pyproject.toml HISTORY.md || { echo "错误：添加文件失败。"; exit 1; }
git commit -m "Release $VERSION" || { echo "错误：提交文件失败。"; exit 1; }
echo "Tagging version $VERSION" || { echo "错误：标记版本号失败。"; exit 1; }
git tag "$VERSION"
git push origin HEAD --tags || { echo "错误：推送版本号失败。"; exit 1; }

# 完成
echo "推送新版本号成功，名字为：$VERSION"
