#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File    :   test_minio_service.py
@Time    :   2024/01/25 11:28:00
<AUTHOR>   l<PERSON><PERSON><PERSON><PERSON>
@Desc    :   测试 MINIO 服务层
"""

import filecmp
import os

from conf.settings import MINIO_OPTIONS
from src.dao.minio.minio import MinioDao


class TestMinioDao:
    """新建和指定 bucket"""

    service = MinioDao(**MINIO_OPTIONS)
    service.bucket_name = "test-bucket"
    bucket_name = service.bucket_name

    def test_create_bucket(self):
        """
        测试创建存储桶
        """
        self.service.create_bucket(self.bucket_name)
        assert self.service.client.bucket_exists(self.bucket_name)

    def test_put_object(self):
        """测试上传对象"""
        obj_list = [
            "this is a string object",
            ["this is a list objet"],
            {"key": "this is a dictionary object"},
        ]
        for index, obj in enumerate(obj_list):
            object_name = f"putobject_{index}"
            self.service.put_object(object_name=f"putobject_{index}", data=obj)
            self.service.remove_object(object_name)

    def test_upload_and_download_file(self):
        """
        测试上传和下载文件
        """
        file_path = "README.md"
        object_name = "README.md"

        self.service.fput_object(
            file_path,
            object_name,
            bucket_name=self.bucket_name,
        )

        downloaded_file_path = "downloaded_README.md"
        self.service.fget_object(
            object_name,
            downloaded_file_path,
            bucket_name=self.bucket_name,
        )

        assert filecmp.cmp(file_path, downloaded_file_path)

        self.service.remove_object(bucket_name=self.bucket_name, object_name=object_name)

        os.remove(downloaded_file_path)

    def test_delete_bucket(self):
        self.service.remove_bucket(self.bucket_name)
