#!/bin/bash

# 设置脚本执行出错时立即退出
set -e

# 帮助信息函数
show_usage() {
    echo "Usage: $0 [-f filename]"
    echo "  -f    Specify a particular zip file to deploy"
    echo "  If no file is specified, the script will use the latest zip file with pattern 'cauc_aac_frontend-*'"
    exit 1
}

# 初始化变量
FRONTEND_DIR="frontend"
ZIP_PREFIX="cauc_aac_frontend"
SPECIFIED_FILE=""

# 处理命令行参数
while getopts "f:h" opt; do
    case $opt in
        f)
            SPECIFIED_FILE="$OPTARG"
            ;;
        h)
            show_usage
            ;;
        \?)
            show_usage
            ;;
    esac
done

# 确保frontend目录存在
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "Creating frontend directory..."
    mkdir -p "$FRONTEND_DIR"
fi

# 根据是否指定文件名选择要部署的文件
if [ -n "$SPECIFIED_FILE" ]; then
    if [ ! -f "$SPECIFIED_FILE" ]; then
        echo "Error: Specified file '$SPECIFIED_FILE' not found!"
        exit 1
    fi
    DEPLOY_FILE="$SPECIFIED_FILE"
else
    # 找到最新的zip文件
    DEPLOY_FILE=$(ls -t ${ZIP_PREFIX}*.zip 2>/dev/null | head -n1)
    if [ -z "$DEPLOY_FILE" ]; then
        echo "Error: No matching zip files found!"
        exit 1
    fi
fi

echo "Using file: $DEPLOY_FILE"

# 创建临时目录
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

# 解压文件
echo "Extracting files..."
unzip -q "$DEPLOY_FILE" -d "$TEMP_DIR"

# 查找dist目录
DIST_DIR=$(find "$TEMP_DIR" -type d -name "dist")
if [ -z "$DIST_DIR" ]; then
    echo "Error: No dist directory found in zip file!"
    exit 1
fi

# 替换文件
echo "Replacing files in frontend directory..."
# 删除原有文件，包括隐藏文件
rm -rf "$FRONTEND_DIR"/{*,.*} 2>/dev/null || true
# 复制新文件，包括隐藏文件
cp -rf "$DIST_DIR"/{*,.*} "$FRONTEND_DIR/" 2>/dev/null || true

echo "Deployment completed successfully!"
