# 贪食蛇 AI - 深度强化学习

这个项目使用深度强化学习（Deep Q-Network, DQN）来训练一个能够自主玩贪食蛇游戏的AI。

## 项目结构

- `game.py`: 原始贪食蛇游戏代码
- `snake_ai.py`: 神经网络模型和训练逻辑
- `train_snake_ai.py`: 训练和游玩AI的主脚本

## 技术细节

这个AI使用了以下技术：

1. **深度Q网络 (DQN)**: 一种强化学习算法，使用神经网络来近似Q值函数
2. **经验回放 (Experience Replay)**: 存储和重用过去的经验来提高学习效率
3. **目标网络 (Target Network)**: 使用单独的网络来计算目标Q值，提高训练稳定性
4. **探索与利用平衡**: 使用ε-贪婪策略在探索新动作和利用已知好动作之间取得平衡

## 状态表示

AI观察到的游戏状态包括：

- 蛇头的位置（相对于棋盘的归一化坐标）
- 食物相对于蛇头的方向
- 危险信息（左、前、右方向是否有障碍物）
- 当前移动方向（上、右、下、左的one-hot编码）
- 蛇的长度（归一化）

## 奖励设计

- 游戏结束（撞到自己）: -10分
- 吃到食物: +10分
- 每一步生存: +0.1分

## 使用方法

### 训练AI

```bash
python train_snake_ai.py --mode train --episodes 500 --render
```

参数说明:
- `--episodes`: 训练回合数
- `--render`: 是否渲染训练过程（每100回合渲染一次）

### 使用训练好的AI玩游戏

```bash
python train_snake_ai.py --mode play --model_path snake_model_final.pt --episodes 5
```

参数说明:
- `--model_path`: 模型文件路径
- `--episodes`: 游戏回合数

## 训练提示

- 训练过程可能需要几百到几千回合才能获得良好的性能
- 可以尝试调整超参数以获得更好的结果：
  - 学习率
  - 探索率及其衰减速度
  - 奖励设计
  - 神经网络结构

## 性能评估

训练过程会生成一个`training_progress.png`图表，显示训练过程中的得分变化。这可以用来评估AI的学习进度。
