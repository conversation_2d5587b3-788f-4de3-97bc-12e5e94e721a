import json
import time
import uuid
import base64
import requests
from typing import Dict, Any, List, Optional, Union, Tuple
import os
import io
import logging

logger = logging.getLogger(__name__)


class ComfyUIClient:
    """Client for interacting with ComfyUI API."""

    def __init__(self, base_url: str = "http://127.0.0.1:8188"):
        self.base_url = base_url
        self.client_id = str(uuid.uuid4())
        self.session = requests.Session()

    def get_history(self) -> Dict[str, Any]:
        """Get the history of workflows."""
        response = self.session.get(f"{self.base_url}/history")
        return response.json()

    def get_workflow_info(self, workflow_id: str) -> Dict[str, Any]:
        """Get information about a specific workflow."""
        history = self.get_history()
        return history.get(workflow_id, {})

    def upload_image(self, image_data: bytes, filename: str = None) -> str:
        """Upload an image to ComfyUI.

        Args:
            image_data: Image data as bytes
            filename: Optional filename

        Returns:
            Name of the uploaded file
        """
        if filename is None:
            filename = f"upload_{uuid.uuid4()}.png"

        files = {
            'image': (filename, image_data, 'image/png')
        }

        response = self.session.post(f"{self.base_url}/upload/image", files=files)
        return response.json().get('name', filename)

    def queue_prompt(self, workflow: Dict[str, Any]) -> str:
        """Queue a prompt for execution.

        Args:
            workflow: Workflow definition

        Returns:
            Prompt ID
        """
        p = {"prompt": workflow, "client_id": self.client_id}
        response = self.session.post(f"{self.base_url}/prompt", json=p)
        return response.json().get('prompt_id')

    def get_image(self, filename: str) -> bytes:
        """Get an image from ComfyUI.

        Args:
            filename: Name of the image file

        Returns:
            Image data as bytes
        """
        response = self.session.get(f"{self.base_url}/view", params={"filename": filename})
        return response.content

    def get_images(self, prompt_id: str) -> List[bytes]:
        """Get all images generated by a prompt.

        Args:
            prompt_id: Prompt ID

        Returns:
            List of image data as bytes
        """
        history = self.get_history()

        # Find the prompt in history
        for workflow_id, workflow_data in history.items():
            for prompt in workflow_data.get('prompts', {}).values():
                if prompt.get('prompt_id') == prompt_id:
                    outputs = prompt.get('outputs', {})
                    images = []

                    for node_id, node_output in outputs.items():
                        for output_data in node_output.values():
                            if isinstance(output_data, list):
                                for item in output_data:
                                    if isinstance(item, dict) and 'filename' in item:
                                        image_data = self.get_image(item['filename'])
                                        images.append(image_data)
                            elif isinstance(output_data, dict) and 'filename' in output_data:
                                image_data = self.get_image(output_data['filename'])
                                images.append(image_data)

                    return images

        return []

    def wait_for_prompt(self, prompt_id: str, timeout: int = 300) -> Tuple[bool, List[bytes]]:
        """Wait for a prompt to complete and get the generated images.

        Args:
            prompt_id: Prompt ID
            timeout: Timeout in seconds

        Returns:
            Tuple of (success, images)
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            # Check if the prompt is done
            response = self.session.get(f"{self.base_url}/prompt", params={"prompt_id": prompt_id})
            data = response.json()

            if data.get('prompt_id') == prompt_id:
                status = data.get('status', {})

                if status.get('status') == 'error':
                    logger.error(f"Error in prompt {prompt_id}: {status.get('error')}")
                    return False, []

                if status.get('status') == 'done':
                    # Get the generated images
                    images = self.get_images(prompt_id)
                    return True, images

            # Wait before checking again
            time.sleep(1)

        logger.warning(f"Timeout waiting for prompt {prompt_id}")
        return False, []

    def run_workflow(self, workflow: Dict[str, Any], timeout: int = 300) -> Tuple[bool, List[bytes]]:
        """Run a workflow and wait for the results.

        Args:
            workflow: Workflow definition
            timeout: Timeout in seconds

        Returns:
            Tuple of (success, images)
        """
        prompt_id = self.queue_prompt(workflow)
        return self.wait_for_prompt(prompt_id, timeout)

    def get_default_workflows(self) -> Dict[str, Dict[str, Any]]:
        """Get a dictionary of default workflows for different tasks."""
        return {
            "text_to_image": self._get_text_to_image_workflow(),
            "image_to_image": self._get_image_to_image_workflow(),
            "text_to_video": self._get_text_to_video_workflow(),
            "image_to_video": self._get_image_to_video_workflow(),
            "image_to_3d": self._get_image_to_3d_workflow(),
            "multi_view_to_3d": self._get_multi_view_to_3d_workflow()
        }

    def find_nodes_by_class_type(self, workflow: Dict[str, Any], class_type: str) -> List[str]:
        """Find nodes in a workflow by their class type.

        Args:
            workflow: Workflow definition
            class_type: Class type to search for

        Returns:
            List of node IDs with the specified class type
        """
        node_ids = []
        for node_id, node_data in workflow.items():
            if node_data.get("class_type") == class_type:
                node_ids.append(node_id)
        return node_ids

    def update_workflow_parameters(self, workflow: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Update a workflow with parameters.

        Args:
            workflow: Workflow definition
            parameters: Parameters to update

        Returns:
            Updated workflow
        """
        # Make a deep copy of the workflow to avoid modifying the original
        import copy
        workflow_copy = copy.deepcopy(workflow)

        # Update latent image dimensions
        if "width" in parameters or "height" in parameters or "batch_size" in parameters:
            empty_latent_nodes = self.find_nodes_by_class_type(workflow_copy, "EmptyLatentImage")
            for node_id in empty_latent_nodes:
                if "width" in parameters:
                    workflow_copy[node_id]["inputs"]["width"] = parameters["width"]
                if "height" in parameters:
                    workflow_copy[node_id]["inputs"]["height"] = parameters["height"]
                if "batch_size" in parameters:
                    workflow_copy[node_id]["inputs"]["batch_size"] = parameters.get("num_images", 1)

        # Update prompts
        if "positive_prompt" in parameters or "negative_prompt" in parameters:
            positive_nodes = []
            negative_nodes = []

            # Find nodes for positive and negative prompts
            for node_id, node_data in workflow_copy.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    if "text" in node_data.get("inputs", {}):
                        text_value = node_data["inputs"]["text"]
                        if text_value == "positive_prompt":
                            positive_nodes.append(node_id)
                        elif text_value == "negative_prompt":
                            negative_nodes.append(node_id)

            # Update positive prompt nodes
            if "positive_prompt" in parameters:
                for node_id in positive_nodes:
                    workflow_copy[node_id]["inputs"]["text"] = parameters["positive_prompt"]

            # Update negative prompt nodes
            if "negative_prompt" in parameters:
                for node_id in negative_nodes:
                    workflow_copy[node_id]["inputs"]["text"] = parameters["negative_prompt"]

        return workflow_copy

    def _get_text_to_image_workflow(self) -> Dict[str, Any]:
        """Get a default text-to-image workflow."""
        return {
  "6": {
    "inputs": {
      "text": "a cat",
      "clip": [
        "30",
        1
      ]
    },
    "class_type": "CLIPTextEncode",
    "_meta": {
      "title": "CLIP Text Encode (Positive Prompt)"
    }
  },
  "8": {
    "inputs": {
      "samples": [
        "31",
        0
      ],
      "vae": [
        "30",
        2
      ]
    },
    "class_type": "VAEDecode",
    "_meta": {
      "title": "VAE解码"
    }
  },
  "9": {
    "inputs": {
      "filename_prefix": "ComfyUI",
      "images": [
        "8",
        0
      ]
    },
    "class_type": "SaveImage",
    "_meta": {
      "title": "保存图像"
    }
  },
  "27": {
    "inputs": {
      "width": 1024,
      "height": 1024,
      "batch_size": 1
    },
    "class_type": "EmptySD3LatentImage",
    "_meta": {
      "title": "空Latent图像（SD3）"
    }
  },
  "30": {
    "inputs": {
      "ckpt_name": "flux1-dev-fp8.safetensors"
    },
    "class_type": "CheckpointLoaderSimple",
    "_meta": {
      "title": "Checkpoint加载器（简易）"
    }
  },
  "31": {
    "inputs": {
      "seed": 752938889566426,
      "steps": 20,
      "cfg": 1,
      "sampler_name": "euler",
      "scheduler": "simple",
      "denoise": 1,
      "model": [
        "30",
        0
      ],
      "positive": [
        "35",
        0
      ],
      "negative": [
        "33",
        0
      ],
      "latent_image": [
        "27",
        0
      ]
    },
    "class_type": "KSampler",
    "_meta": {
      "title": "K采样器"
    }
  },
  "33": {
    "inputs": {
      "text": "",
      "clip": [
        "30",
        1
      ]
    },
    "class_type": "CLIPTextEncode",
    "_meta": {
      "title": "CLIP Text Encode (Negative Prompt)"
    }
  },
  "35": {
    "inputs": {
      "guidance": 3.5,
      "conditioning": [
        "6",
        0
      ]
    },
    "class_type": "FluxGuidance",
    "_meta": {
      "title": "Flux引导"
    }
  }
}

    def _get_image_to_image_workflow(self) -> Dict[str, Any]:
        """Get a default image-to-image workflow."""
        return {
            "3": {
                "inputs": {
                    "seed": 123456789,
                    "steps": 20,
                    "cfg": 7,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 0.75,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["10", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "dreamshaper_8.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "6": {
                "inputs": {
                    "text": "positive_prompt",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "7": {
                "inputs": {
                    "text": "negative_prompt",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "9": {
                "inputs": {
                    "filename_prefix": "output",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage"
            },
            "10": {
                "inputs": {
                    "pixels": ["11", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEEncode"
            },
            "11": {
                "inputs": {
                    "image": "image_input",
                    "upload": "image"
                },
                "class_type": "LoadImage"
            }
        }

    def _get_text_to_video_workflow(self) -> Dict[str, Any]:
        """Get a default text-to-video workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper text-to-video model
        return {
            "3": {
                "inputs": {
                    "seed": 123456789,
                    "steps": 20,
                    "cfg": 7,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 1,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["5", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "dreamshaper_8.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "5": {
                "inputs": {
                    "width": 512,
                    "height": 512,
                    "batch_size": 8  # Generate multiple frames
                },
                "class_type": "EmptyLatentImage"
            },
            "6": {
                "inputs": {
                    "text": "positive_prompt",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "7": {
                "inputs": {
                    "text": "negative_prompt",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "9": {
                "inputs": {
                    "filename_prefix": "output",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage"
            }
        }

    def _get_image_to_video_workflow(self) -> Dict[str, Any]:
        """Get a default image-to-video workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper image-to-video model
        return {
            "3": {
                "inputs": {
                    "seed": 123456789,
                    "steps": 20,
                    "cfg": 7,
                    "sampler_name": "euler",
                    "scheduler": "normal",
                    "denoise": 0.75,
                    "model": ["4", 0],
                    "positive": ["6", 0],
                    "negative": ["7", 0],
                    "latent_image": ["10", 0]
                },
                "class_type": "KSampler"
            },
            "4": {
                "inputs": {
                    "ckpt_name": "dreamshaper_8.safetensors"
                },
                "class_type": "CheckpointLoaderSimple"
            },
            "6": {
                "inputs": {
                    "text": "positive_prompt",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "7": {
                "inputs": {
                    "text": "negative_prompt",
                    "clip": ["4", 1]
                },
                "class_type": "CLIPTextEncode"
            },
            "8": {
                "inputs": {
                    "samples": ["3", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEDecode"
            },
            "9": {
                "inputs": {
                    "filename_prefix": "output",
                    "images": ["8", 0]
                },
                "class_type": "SaveImage"
            },
            "10": {
                "inputs": {
                    "pixels": ["11", 0],
                    "vae": ["4", 2]
                },
                "class_type": "VAEEncode"
            },
            "11": {
                "inputs": {
                    "image": "image_input",
                    "upload": "image"
                },
                "class_type": "LoadImage"
            }
        }

    def _get_image_to_3d_workflow(self) -> Dict[str, Any]:
        """Get a default image-to-3D workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper image-to-3D model
        return {
            "3": {
                "inputs": {
                    "image": "image_input",
                    "upload": "image"
                },
                "class_type": "LoadImage"
            },
            "4": {
                "inputs": {
                    "images": ["3", 0]
                },
                "class_type": "ImageTo3D"  # Placeholder class
            },
            "5": {
                "inputs": {
                    "filename_prefix": "output",
                    "model": ["4", 0]
                },
                "class_type": "Save3DModel"  # Placeholder class
            }
        }

    def _get_multi_view_to_3d_workflow(self) -> Dict[str, Any]:
        """Get a default multi-view-to-3D workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper multi-view-to-3D model
        return {
            "3": {
                "inputs": {
                    "image": "front_view",
                    "upload": "image"
                },
                "class_type": "LoadImage"
            },
            "4": {
                "inputs": {
                    "image": "side_view",
                    "upload": "image"
                },
                "class_type": "LoadImage"
            },
            "5": {
                "inputs": {
                    "image": "back_view",
                    "upload": "image"
                },
                "class_type": "LoadImage"
            },
            "6": {
                "inputs": {
                    "front": ["3", 0],
                    "side": ["4", 0],
                    "back": ["5", 0]
                },
                "class_type": "MultiViewTo3D"  # Placeholder class
            },
            "7": {
                "inputs": {
                    "filename_prefix": "output",
                    "model": ["6", 0]
                },
                "class_type": "Save3DModel"  # Placeholder class
            }
        }
