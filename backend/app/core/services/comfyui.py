import logging
import time
import uuid
from typing import Dict, Any, List, Tuple

import requests

logger = logging.getLogger(__name__)


class ComfyUIClient:
    """Client for interacting with ComfyUI API."""

    def __init__(self, base_url: str = "http://127.0.0.1:8188"):
        self.base_url = base_url
        self.client_id = str(uuid.uuid4())
        self.session = requests.Session()

    def get_history(self, prompt_id: Optional[str] = None) -> Dict[str, Any]:
        """Get the history of workflows.

        Args:
            prompt_id: Optional prompt ID to get specific prompt history

        Returns:
            History data. If prompt_id is provided, returns specific prompt history.
            If prompt_id is None, returns all history.
        """
        if prompt_id:
            response = self.session.get(f"{self.base_url}/history/{prompt_id}")
        else:
            response = self.session.get(f"{self.base_url}/history")
        return response.json()

    def get_workflow_info(self, workflow_id: str) -> Dict[str, Any]:
        """Get information about a specific workflow."""
        history = self.get_history()
        return history.get(workflow_id, {})

    def upload_image(self, image_data: bytes, filename: str = None) -> str:
        """Upload an image to ComfyUI.

        Args:
            image_data: Image data as bytes
            filename: Optional filename

        Returns:
            Name of the uploaded file
        """
        if filename is None:
            filename = f"upload_{uuid.uuid4()}.png"

        files = {
            'image': (filename, image_data, 'image/png')
        }

        response = self.session.post(f"{self.base_url}/upload/image", files=files)
        return response.json().get('name', filename)

    def queue_prompt(self, workflow: Dict[str, Any]) -> str:
        """Queue a prompt for execution.

        Args:
            workflow: Workflow definition

        Returns:
            Prompt ID
        """
        p = {"prompt": workflow, "client_id": self.client_id}
        response = self.session.post(f"{self.base_url}/prompt", json=p)
        return response.json().get('prompt_id')

    def get_image(self, filename: str) -> bytes:
        """Get an image from ComfyUI.

        Args:
            filename: Name of the image file

        Returns:
            Image data as bytes
        """
        response = self.session.get(f"{self.base_url}/view", params={"filename": filename})
        return response.content

    def get_images(self, prompt_id: str) -> List[bytes]:
        """Get all images generated by a prompt.

        Args:
            prompt_id: Prompt ID

        Returns:
            List of image data as bytes
        """
        history = self.get_history()

        # Find the prompt in history
        for workflow_id, workflow_data in history.items():
            for prompt in workflow_data.get('prompts', {}).values():
                if prompt.get('prompt_id') == prompt_id:
                    outputs = prompt.get('outputs', {})
                    images = []

                    for node_id, node_output in outputs.items():
                        for output_data in node_output.values():
                            if isinstance(output_data, list):
                                for item in output_data:
                                    if isinstance(item, dict) and 'filename' in item:
                                        image_data = self.get_image(item['filename'])
                                        images.append(image_data)
                            elif isinstance(output_data, dict) and 'filename' in output_data:
                                image_data = self.get_image(output_data['filename'])
                                images.append(image_data)

                    return images

        return []

    def wait_for_prompt(self, prompt_id: str, timeout: int = 300) -> Tuple[bool, List[bytes]]:
        """Wait for a prompt to complete and get the generated images.

        Args:
            prompt_id: Prompt ID
            timeout: Timeout in seconds

        Returns:
            Tuple of (success, images)
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            # Check if the prompt is done
            response = self.session.get(f"{self.base_url}/prompt", params={"prompt_id": prompt_id})
            data = response.json()

            if data.get('prompt_id') == prompt_id:
                status = data.get('status', {})

                if status.get('status') == 'error':
                    logger.error(f"Error in prompt {prompt_id}: {status.get('error')}")
                    return False, []

                if status.get('status') == 'done':
                    # Get the generated images
                    images = self.get_images(prompt_id)
                    return True, images

            # Wait before checking again
            time.sleep(1)

        logger.warning(f"Timeout waiting for prompt {prompt_id}")
        return False, []

    def run_workflow(self, workflow: Dict[str, Any], timeout: int = 300) -> Tuple[bool, List[bytes]]:
        """Run a workflow and wait for the results.

        Args:
            workflow: Workflow definition
            timeout: Timeout in seconds

        Returns:
            Tuple of (success, images)
        """
        prompt_id = self.queue_prompt(workflow)
        return self.wait_for_prompt(prompt_id, timeout)

    def get_default_workflows(self) -> Dict[str, Dict[str, Any]]:
        """Get a dictionary of default workflows for different tasks."""
        return {
            "text_to_image": self._get_text_to_image_workflow(),
            "image_to_image": self._get_image_to_image_workflow(),
            "text_to_video": self._get_text_to_video_workflow(),
            "image_to_video": self._get_image_to_video_workflow(),
            "image_to_3d": self._get_image_to_3d_workflow(),
            "multi_view_to_3d": self._get_multi_view_to_3d_workflow()
        }

    def find_nodes_by_class_type(self, workflow: Dict[str, Any], class_types: List[str]) -> List[str]:
        """Find nodes in a workflow by their class type.

        Args:
            workflow: Workflow definition
            class_types: Class types to search for

        Returns:
            List of node IDs with the specified class type
        """
        node_ids = []
        for node_id, node_data in workflow.items():
            if node_data.get("class_type") in class_types:
                node_ids.append(node_id)
        return node_ids

    def update_workflow_parameters(self, workflow: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Update a workflow with parameters.

        Args:
            workflow: Workflow definition
            parameters: Parameters to update

        Returns:
            Updated workflow
        """
        # Make a deep copy of the workflow to avoid modifying the original
        import copy
        workflow_copy = copy.deepcopy(workflow)

        # Update latent image dimensions
        if "width" in parameters or "height" in parameters or "batch_size" in parameters:
            empty_latent_nodes = self.find_nodes_by_class_type(workflow_copy, ["EmptyLatentImage", "EmptySD3LatentImage"])
            for node_id in empty_latent_nodes:
                if "width" in parameters:
                    workflow_copy[node_id]["inputs"]["width"] = parameters["width"]
                if "height" in parameters:
                    workflow_copy[node_id]["inputs"]["height"] = parameters["height"]
                if "batch_size" in parameters:
                    workflow_copy[node_id]["inputs"]["batch_size"] = parameters.get("num_images", 1)

        # Update prompts
        if "positive_prompt" in parameters or "negative_prompt" in parameters:
            positive_nodes = []
            negative_nodes = []

            # Find nodes for positive and negative prompts
            for node_id, node_data in workflow_copy.items():
                if node_data.get("class_type") == "CLIPTextEncode":
                    if "text" in node_data.get("inputs", {}):
                        text_value = node_data["inputs"]["text"]
                        if text_value == "positive_prompt":
                            positive_nodes.append(node_id)
                        elif text_value == "negative_prompt":
                            negative_nodes.append(node_id)

            # Update positive prompt nodes
            if "positive_prompt" in parameters:
                for node_id in positive_nodes:
                    workflow_copy[node_id]["inputs"]["text"] = parameters["positive_prompt"]

            # Update negative prompt nodes
            if "negative_prompt" in parameters:
                for node_id in negative_nodes:
                    workflow_copy[node_id]["inputs"]["text"] = parameters["negative_prompt"]

        return workflow_copy

    def _get_text_to_image_workflow(self) -> Dict[str, Any]:
        """Get a default text-to-image workflow."""
        return {
            "6": {
                "inputs": {
                    "text": "positive_prompt",
                    "clip": [
                        "30",
                        1
                    ]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {
                    "title": "CLIP Text Encode (Positive Prompt)"
                }
            },
            "8": {
                "inputs": {
                    "samples": [
                        "31",
                        0
                    ],
                    "vae": [
                        "30",
                        2
                    ]
                },
                "class_type": "VAEDecode",
                "_meta": {
                    "title": "VAE解码"
                }
            },
            "9": {
                "inputs": {
                    "filename_prefix": "ComfyUI",
                    "images": [
                        "8",
                        0
                    ]
                },
                "class_type": "SaveImage",
                "_meta": {
                    "title": "保存图像"
                }
            },
            "27": {
                "inputs": {
                    "width": 1024,
                    "height": 1024,
                    "batch_size": 1
                },
                "class_type": "EmptySD3LatentImage",
                "_meta": {
                    "title": "空Latent图像（SD3）"
                }
            },
            "30": {
                "inputs": {
                    "ckpt_name": "flux1-dev-fp8.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {
                    "title": "Checkpoint加载器（简易）"
                }
            },
            "31": {
                "inputs": {
                    "seed": 752938889566426,
                    "steps": 20,
                    "cfg": 1,
                    "sampler_name": "euler",
                    "scheduler": "simple",
                    "denoise": 1,
                    "model": [
                        "30",
                        0
                    ],
                    "positive": [
                        "35",
                        0
                    ],
                    "negative": [
                        "33",
                        0
                    ],
                    "latent_image": [
                        "27",
                        0
                    ]
                },
                "class_type": "KSampler",
                "_meta": {
                    "title": "K采样器"
                }
            },
            "33": {
                "inputs": {
                    "text": "",
                    "clip": [
                        "30",
                        1
                    ]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {
                    "title": "CLIP Text Encode (Negative Prompt)"
                }
            },
            "35": {
                "inputs": {
                    "guidance": 3.5,
                    "conditioning": [
                        "6",
                        0
                    ]
                },
                "class_type": "FluxGuidance",
                "_meta": {
                    "title": "Flux引导"
                }
            }
        }

    def _get_image_to_image_workflow(self) -> Dict[str, Any]:
        """Get a default image-to-image workflow."""
        return {
            "6": {
                "inputs": {
                    "text": "positive_prompt",
                    "clip": [
                        "11",
                        0
                    ]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {
                    "title": "CLIP文本编码"
                }
            },
            "8": {
                "inputs": {
                    "samples": [
                        "13",
                        1
                    ],
                    "vae": [
                        "10",
                        0
                    ]
                },
                "class_type": "VAEDecode",
                "_meta": {
                    "title": "VAE解码"
                }
            },
            "10": {
                "inputs": {
                    "vae_name": "ae.safetensors"
                },
                "class_type": "VAELoader",
                "_meta": {
                    "title": "加载VAE"
                }
            },
            "11": {
                "inputs": {
                    "clip_name1": "t5xxl_fp16.safetensors",
                    "clip_name2": "clip_l.safetensors",
                    "type": "flux",
                    "device": "default"
                },
                "class_type": "DualCLIPLoader",
                "_meta": {
                    "title": "双CLIP加载器"
                }
            },
            "12": {
                "inputs": {
                    "unet_name": "flux1-fill-dev.safetensors",
                    "weight_dtype": "default"
                },
                "class_type": "UNETLoader",
                "_meta": {
                    "title": "UNet加载器"
                }
            },
            "13": {
                "inputs": {
                    "noise": [
                        "25",
                        0
                    ],
                    "guider": [
                        "22",
                        0
                    ],
                    "sampler": [
                        "16",
                        0
                    ],
                    "sigmas": [
                        "17",
                        0
                    ],
                    "latent_image": [
                        "52",
                        0
                    ]
                },
                "class_type": "SamplerCustomAdvanced",
                "_meta": {
                    "title": "自定义采样器（高级）"
                }
            },
            "16": {
                "inputs": {
                    "sampler_name": "euler"
                },
                "class_type": "KSamplerSelect",
                "_meta": {
                    "title": "K采样器选择"
                }
            },
            "17": {
                "inputs": {
                    "scheduler": "simple",
                    "steps": 20,
                    "denoise": 0.75,
                    "model": [
                        "28",
                        0
                    ]
                },
                "class_type": "BasicScheduler",
                "_meta": {
                    "title": "基本调度器"
                }
            },
            "22": {
                "inputs": {
                    "model": [
                        "28",
                        0
                    ],
                    "conditioning": [
                        "29",
                        0
                    ]
                },
                "class_type": "BasicGuider",
                "_meta": {
                    "title": "基本引导器"
                }
            },
            "25": {
                "inputs": {
                    "noise_seed": 6
                },
                "class_type": "RandomNoise",
                "_meta": {
                    "title": "随机噪波"
                }
            },
            "26": {
                "inputs": {
                    "images": [
                        "8",
                        0
                    ]
                },
                "class_type": "PreviewImage",
                "_meta": {
                    "title": "预览图像"
                }
            },
            "28": {
                "inputs": {
                    "max_shift": 1.1500000000000001,
                    "base_shift": 0.5,
                    "width": [
                        "48",
                        1
                    ],
                    "height": [
                        "48",
                        2
                    ],
                    "model": [
                        "12",
                        0
                    ]
                },
                "class_type": "ModelSamplingFlux",
                "_meta": {
                    "title": "采样算法（Flux）"
                }
            },
            "29": {
                "inputs": {
                    "guidance": 3.5,
                    "conditioning": [
                        "6",
                        0
                    ]
                },
                "class_type": "FluxGuidance",
                "_meta": {
                    "title": "Flux引导"
                }
            },
            "48": {
                "inputs": {
                    "resolution": "1024x1024 (1.0)",
                    "batch_size": 1,
                    "width_override": 0,
                    "height_override": 0
                },
                "class_type": "SDXLEmptyLatentSizePicker+",
                "_meta": {
                    "title": "🔧 Empty Latent Size Picker"
                }
            },
            "50": {
                "inputs": {
                    "image": "girl.png"
                },
                "class_type": "LoadImage",
                "_meta": {
                    "title": "加载图像"
                }
            },
            "51": {
                "inputs": {
                    "upscale_method": "lanczos",
                    "width": 1024,
                    "height": 1024,
                    "crop": "disabled",
                    "image": [
                        "50",
                        0
                    ]
                },
                "class_type": "ImageScale",
                "_meta": {
                    "title": "缩放图像"
                }
            },
            "52": {
                "inputs": {
                    "pixels": [
                        "51",
                        0
                    ],
                    "vae": [
                        "10",
                        0
                    ]
                },
                "class_type": "VAEEncode",
                "_meta": {
                    "title": "VAE编码"
                }
            }
        }

    def _get_text_to_video_workflow(self) -> Dict[str, Any]:
        """Get a default text-to-video workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper text-to-video model
        return {
        }

    def _get_image_to_video_workflow(self) -> Dict[str, Any]:
        """Get a default image-to-video workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper image-to-video model
        return {
        }

    def _get_image_to_3d_workflow(self) -> Dict[str, Any]:
        """Get a default image-to-3D workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper image-to-3D model
        return {
        }

    def _get_multi_view_to_3d_workflow(self) -> Dict[str, Any]:
        """Get a default multi-view-to-3D workflow."""
        # This is a simplified placeholder workflow
        # In a real implementation, you would use a proper multi-view-to-3D model
        return {
        }
