import json
import requests
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class LLMClient:
    """Client for interacting with Large Language Models."""

    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1", model: str = "gpt-3.5-turbo"):
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        })

    def generate_prompts(self, text: str, mode: str = "image") -> Dict[str, str]:
        """Generate positive and negative prompts based on input text.

        Args:
            text: Input text to generate prompts from
            mode: Type of prompts to generate (image, video, 3d)

        Returns:
            Dictionary with positive_prompt and negative_prompt
        """
        try:
            # Define system prompts based on mode
            if mode == "image":
                system_prompt = (
                    "You are an expert at creating Stable Diffusion prompts. "
                    "Given a text description, create a detailed positive prompt that will generate a high-quality image, "
                    "and a negative prompt to avoid common issues. "
                    "Format your response as a JSON object with 'positive_prompt' and 'negative_prompt' keys."
                )
            elif mode == "video":
                system_prompt = (
                    "You are an expert at creating video generation prompts. "
                    "Given a text description, create a detailed positive prompt that will generate a high-quality video, "
                    "and a negative prompt to avoid common issues. "
                    "Format your response as a JSON object with 'positive_prompt' and 'negative_prompt' keys."
                )
            elif mode == "3d":
                system_prompt = (
                    "You are an expert at creating 3D model generation prompts. "
                    "Given a text description, create a detailed positive prompt that will generate a high-quality 3D model, "
                    "and a negative prompt to avoid common issues. "
                    "Format your response as a JSON object with 'positive_prompt' and 'negative_prompt' keys."
                )
            else:
                system_prompt = (
                    "You are an expert at creating generation prompts. "
                    "Given a text description, create a detailed positive prompt and a negative prompt. "
                    "Format your response as a JSON object with 'positive_prompt' and 'negative_prompt' keys."
                )

            # Create messages for the API call
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": text}
            ]

            # Make API call
            response = self.session.post(
                f"{self.base_url}/chat/completions",
                json={
                    "model": self.model,
                    "messages": messages,
                    "temperature": 0.7,
                    "max_tokens": 500,
                    "response_format": {"type": "json_object"}
                }
            )

            if response.status_code != 200:
                logger.error(f"Error generating prompts: {response.text}")
                return {
                    "positive_prompt": text,
                    "negative_prompt": "blurry, bad quality, distorted"
                }

            # Parse response
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "{}")

            try:
                prompts = json.loads(content)
                return {
                    "positive_prompt": prompts.get("positive_prompt", text),
                    "negative_prompt": prompts.get("negative_prompt", "blurry, bad quality, distorted")
                }
            except json.JSONDecodeError:
                logger.error(f"Error parsing LLM response: {content}")
                return {
                    "positive_prompt": text,
                    "negative_prompt": "blurry, bad quality, distorted"
                }

        except Exception as e:
            logger.exception(f"Error in prompt generation: {str(e)}")
            return {
                "positive_prompt": text,
                "negative_prompt": "blurry, bad quality, distorted"
            }

    def generate_text(self, prompt: str, max_tokens: int = 500) -> str:
        """Generate text based on a prompt.

        Args:
            prompt: Input prompt
            max_tokens: Maximum number of tokens to generate

        Returns:
            Generated text
        """
        try:
            # Make API call
            response = self.session.post(
                f"{self.base_url}/chat/completions",
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.7,
                    "max_tokens": max_tokens
                }
            )

            if response.status_code != 200:
                logger.error(f"Error generating text: {response.text}")
                return ""

            # Parse response
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")

            return content

        except Exception as e:
            logger.exception(f"Error in text generation: {str(e)}")
            return ""
