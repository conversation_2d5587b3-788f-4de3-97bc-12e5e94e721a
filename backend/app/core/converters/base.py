from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Type
from pydantic import BaseModel
import uuid
from datetime import datetime

from ..media.base import AbstractMedia, MediaContent


class ConversionTask(BaseModel):
    """Base model for conversion tasks."""
    id: str = ""
    user_id: Optional[str] = None
    status: str = "pending"  # pending, processing, completed, failed
    created_at: datetime = datetime.now()
    updated_at: datetime = datetime.now()
    input_media_ids: List[str] = []
    output_media_ids: List[str] = []
    parameters: Dict[str, Any] = {}
    progress: float = 0.0
    current_step: Optional[str] = None
    error_message: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        if not self.id:
            self.id = str(uuid.uuid4())


class AbstractConverter(ABC):
    """Abstract base class for all media converters."""

    @property
    @abstractmethod
    def input_types(self) -> List[Type[AbstractMedia]]:
        """Get the list of input media types this converter supports.

        Returns:
            List of AbstractMedia subclasses
        """
        pass

    @property
    @abstractmethod
    def output_types(self) -> List[Type[AbstractMedia]]:
        """Get the list of output media types this converter produces.

        Returns:
            List of AbstractMedia subclasses
        """
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """Get the name of the converter.

        Returns:
            Converter name
        """
        pass

    @property
    @abstractmethod
    def description(self) -> str:
        """Get the description of the converter.

        Returns:
            Converter description
        """
        pass

    @abstractmethod
    def validate_inputs(self, inputs: List[AbstractMedia]) -> bool:
        """Validate that the inputs are compatible with this converter.

        Args:
            inputs: List of input media

        Returns:
            True if inputs are valid, False otherwise
        """
        pass

    @abstractmethod
    def convert(self, task: ConversionTask, inputs: List[AbstractMedia]) -> List[AbstractMedia]:
        """Convert input media to output media.

        Args:
            task: Conversion task
            inputs: List of input media

        Returns:
            List of output media
        """
        pass

    @abstractmethod
    def get_progress(self, task_id: str) -> Dict[str, Any]:
        """Get the progress of a conversion task.

        Args:
            task_id: ID of the conversion task

        Returns:
            Dictionary with progress information including:
            - progress: float between 0 and 1
            - current_step: string describing the current processing step
        """
        pass

    @abstractmethod
    def cancel(self, task_id: str) -> bool:
        """Cancel a conversion task.

        Args:
            task_id: ID of the conversion task

        Returns:
            True if the task was cancelled, False otherwise
        """
        pass


class ConverterRegistry:
    """Registry of all available converters."""

    _converters: List[AbstractConverter] = []

    @classmethod
    def register(cls, converter: AbstractConverter) -> None:
        """Register a converter.

        Args:
            converter: Converter to register
        """
        cls._converters.append(converter)

    @classmethod
    def get_all(cls) -> List[AbstractConverter]:
        """Get all registered converters.

        Returns:
            List of all registered converters
        """
        return cls._converters

    @classmethod
    def find_converter(cls, input_types: List[Type[AbstractMedia]], output_type: Type[AbstractMedia]) -> Optional[AbstractConverter]:
        """Find a converter that can convert from the input types to the output type.

        Args:
            input_types: List of input media types
            output_type: Output media type

        Returns:
            Converter if found, None otherwise
        """
        for converter in cls._converters:
            # Check if all input types are supported
            if all(input_type in converter.input_types for input_type in input_types):
                # Check if the output type is produced
                if output_type in converter.output_types:
                    return converter
        return None

    @classmethod
    def find_conversion_path(cls, input_types: List[Type[AbstractMedia]], output_type: Type[AbstractMedia]) -> Optional[List[AbstractConverter]]:
        """Find a path of converters that can convert from the input types to the output type.

        Args:
            input_types: List of input media types
            output_type: Output media type

        Returns:
            List of converters if a path is found, None otherwise
        """
        # Simple BFS to find a path
        visited = set()
        queue = [(input_types, [])]

        while queue:
            current_types, path = queue.pop(0)

            # Check if we've reached the output type
            if output_type in current_types:
                return path

            # Try all converters
            for converter in cls._converters:
                if all(input_type in converter.input_types for input_type in current_types):
                    new_types = current_types + converter.output_types
                    new_path = path + [converter]

                    # Convert to a hashable type for visited set
                    types_key = tuple(sorted([t.__name__ for t in new_types]))

                    if types_key not in visited:
                        visited.add(types_key)
                        queue.append((new_types, new_path))

        return None
