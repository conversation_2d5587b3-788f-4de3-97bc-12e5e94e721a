from typing import Any, Dict, <PERSON>, Optional
import os
import json
import base64
from datetime import datetime

from .base import AbstractMedia, MediaContent


class AudioMedia(AbstractMedia):
    """Class for audio media."""
    
    def __init__(self, audio_data: Optional[bytes] = None, metadata: Dict[str, Any] = None):
        self.audio_data = audio_data
        self._metadata = MediaContent(
            content_type="audio",
            metadata=metadata or {}
        )
    
    def load(self, source: Union[str, bytes, Dict[str, Any]]) -> None:
        """Load audio content from a source.
        
        Args:
            source: Can be a file path, bytes data, or a dictionary with content
        """
        if isinstance(source, str):
            if os.path.isfile(source):
                with open(source, 'rb') as f:
                    self.audio_data = f.read()
                self._metadata.file_path = source
                
                # Try to extract audio metadata (duration, etc.) if possible
                # This would require additional libraries like pydub or librosa
                # For simplicity, we'll just store the file size for now
                self._metadata.metadata.update({
                    "file_size": len(self.audio_data),
                    "file_extension": os.path.splitext(source)[1][1:]
                })
            elif source.startswith('data:audio'):
                # Handle data URL
                header, encoded = source.split(",", 1)
                self.audio_data = base64.b64decode(encoded)
                
                # Extract MIME type from header
                mime_type = header.split(":")[1].split(";")[0]
                self._metadata.metadata.update({
                    "file_size": len(self.audio_data),
                    "mime_type": mime_type
                })
        elif isinstance(source, bytes):
            self.audio_data = source
            self._metadata.metadata.update({
                "file_size": len(self.audio_data)
            })
        elif isinstance(source, dict):
            if 'file_path' in source and os.path.isfile(source['file_path']):
                with open(source['file_path'], 'rb') as f:
                    self.audio_data = f.read()
                self._metadata.file_path = source['file_path']
                self._metadata.metadata.update({
                    "file_size": len(self.audio_data),
                    "file_extension": os.path.splitext(source['file_path'])[1][1:]
                })
            
            if 'metadata' in source:
                self._metadata.metadata.update(source['metadata'])
    
    def save(self, destination: str) -> str:
        """Save audio content to a destination.
        
        Args:
            destination: Directory path where to save the audio
            
        Returns:
            Path to the saved file
        """
        if not self.audio_data:
            raise ValueError("No audio data to save")
        
        os.makedirs(destination, exist_ok=True)
        
        # Determine file extension
        ext = "mp3"  # Default extension
        if "file_extension" in self._metadata.metadata:
            ext = self._metadata.metadata["file_extension"]
        elif "mime_type" in self._metadata.metadata:
            mime_type = self._metadata.metadata["mime_type"]
            if mime_type == "audio/mp3" or mime_type == "audio/mpeg":
                ext = "mp3"
            elif mime_type == "audio/wav":
                ext = "wav"
            elif mime_type == "audio/ogg":
                ext = "ogg"
        
        file_path = os.path.join(destination, f"{self._metadata.id}.{ext}")
        
        with open(file_path, 'wb') as f:
            f.write(self.audio_data)
        
        self._metadata.file_path = file_path
        self._metadata.updated_at = datetime.now()
        
        # Save metadata
        metadata_path = os.path.join(destination, f"{self._metadata.id}.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            f.write(self._metadata.json())
        
        return file_path
    
    def get_metadata(self) -> MediaContent:
        """Get metadata about the audio content.
        
        Returns:
            MediaContent object with metadata
        """
        return self._metadata
    
    def get_preview(self) -> str:
        """Get a preview of the audio as a base64 data URL.
        
        Returns:
            Base64 data URL of the audio
        """
        if not self.audio_data:
            return ""
        
        # Determine MIME type
        mime_type = "audio/mp3"  # Default MIME type
        if "mime_type" in self._metadata.metadata:
            mime_type = self._metadata.metadata["mime_type"]
        elif "file_extension" in self._metadata.metadata:
            ext = self._metadata.metadata["file_extension"]
            if ext == "mp3":
                mime_type = "audio/mp3"
            elif ext == "wav":
                mime_type = "audio/wav"
            elif ext == "ogg":
                mime_type = "audio/ogg"
        
        encoded = base64.b64encode(self.audio_data).decode('utf-8')
        return f"data:{mime_type};base64,{encoded}"
