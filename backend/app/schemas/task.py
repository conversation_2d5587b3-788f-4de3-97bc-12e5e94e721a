from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

from .base import TimestampSchema, IDSchema
from .media import Media


class TaskBase(BaseModel):
    """Base schema for task data."""
    user_id: Optional[str] = None
    status: str = "pending"  # pending, processing, completed, failed
    task_type: str
    progress: float = 0.0
    current_step: Optional[str] = None  # Description of current processing step
    error_message: Optional[str] = None
    parameters: Dict[str, Any] = Field(default_factory=dict)


class TaskCreate(TaskBase):
    """Schema for creating a new task."""
    input_media_ids: List[str] = Field(default_factory=list)


class TaskUpdate(BaseModel):
    """Schema for updating a task."""
    status: Optional[str] = None
    progress: Optional[float] = None
    current_step: Optional[str] = None
    error_message: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None


class TaskInDBBase(TaskBase, IDSchema, TimestampSchema):
    """Base schema for task in database."""
    pass


class Task(TaskInDBBase):
    """Schema for task response."""
    input_media: List[Media] = Field(default_factory=list)
    output_media: List[Media] = Field(default_factory=list)


class TaskInDB(TaskInDBBase):
    """Schema for task in database."""
    pass


class TaskWithProgress(BaseModel):
    """Schema for task progress response."""
    id: str
    status: str
    progress: float
    current_step: Optional[str] = None
    error_message: Optional[str] = None
