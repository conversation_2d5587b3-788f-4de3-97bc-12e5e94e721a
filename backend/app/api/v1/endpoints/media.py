from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlmodel import Session, select
import os
import uuid
import json
import base64
from datetime import datetime

from ....database import get_session
from ....models.media import Media
from ....schemas.media import MediaCreate, Media as MediaSchema, MediaPreview, MediaUpload
from ....config import settings
from ....core.media.text import TextMedia
from ....core.media.image import ImageMedia
from ....core.media.audio import AudioMedia
from ....core.media.video import VideoMedia
from ....core.media.model3d import Model3DMedia

router = APIRouter()


def get_media_class(media_type: str):
    """Get the media class for a media type."""
    media_classes = {
        "text": TextMedia,
        "image": ImageMedia,
        "audio": AudioMedia,
        "video": VideoMedia,
        "model3d": Model3DMedia
    }
    return media_classes.get(media_type)


@router.post("/upload/", response_model=MediaSchema)
async def upload_media(
    file: UploadFile = File(...),
    media_type: str = Form(...),
    content_type: str = Form(""),
    metadata: str = Form("{}"),
    user_id: Optional[str] = Form(None),
    session: Session = Depends(get_session)
):
    """Upload a media file."""
    # Validate media type
    if media_type not in ["text", "image", "audio", "video", "model3d"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid media type: {media_type}"
        )

    # Read file content
    file_content = await file.read()

    # Create media object
    media_class = get_media_class(media_type)
    if not media_class:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Media type {media_type} not supported"
        )

    media = media_class(file_content)

    # Save media file
    media_path = media.save(settings.MEDIA_STORAGE_PATH)

    # Parse metadata
    try:
        metadata_dict = json.loads(metadata)
    except json.JSONDecodeError:
        metadata_dict = {}

    # Create media record
    media_metadata = media.get_metadata()
    db_media = Media(
        id=media_metadata.id,
        user_id=user_id,
        media_type=media_type,
        file_path=media_path,
        content_type=content_type or media_metadata.content_type,
        metadatas=metadata_dict or media_metadata.metadata
    )
    session.add(db_media)
    session.commit()
    session.refresh(db_media)

    return db_media


@router.post("/text/", response_model=MediaSchema)
def create_text_media(
    text_content: str,
    user_id: Optional[str] = None,
    metadata: Optional[dict] = None,
    session: Session = Depends(get_session)
):
    """Create a text media."""
    # Create text media object
    text_media = TextMedia(content=text_content)

    # Save media file
    media_path = text_media.save(settings.MEDIA_STORAGE_PATH)

    # Create media record
    media_metadata = text_media.get_metadata()
    db_media = Media(
        id=media_metadata.id,
        user_id=user_id,
        media_type="text",
        file_path=media_path,
        content_type="text/plain",
        metadatas=metadata or media_metadata.metadata
    )
    session.add(db_media)
    session.commit()
    session.refresh(db_media)

    return db_media


@router.get("/{media_id}", response_model=MediaSchema)
def get_media(media_id: str, session: Session = Depends(get_session)):
    """Get a media by ID."""
    media = session.get(Media, media_id)
    if not media:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Media {media_id} not found"
        )

    return media


@router.get("/{media_id}/file")
def get_media_file(media_id: str, session: Session = Depends(get_session)):
    """Get a media file."""
    media = session.get(Media, media_id)
    if not media:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Media {media_id} not found"
        )

    if not media.file_path or not os.path.isfile(media.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Media file not found"
        )

    return FileResponse(media.file_path)


@router.get("/{media_id}/preview", response_model=MediaPreview)
def get_media_preview(media_id: str, session: Session = Depends(get_session)):
    """Get a preview of a media."""
    media = session.get(Media, media_id)
    if not media:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Media {media_id} not found"
        )

    if not media.file_path or not os.path.isfile(media.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Media file not found"
        )

    # Load media object
    media_class = get_media_class(media.media_type)
    if not media_class:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Media type {media.media_type} not supported"
        )

    media_obj = media_class()
    media_obj.load(media.file_path)

    # Get preview
    preview = media_obj.get_preview()

    # Create preview URL
    if isinstance(preview, str) and preview.startswith("data:"):
        preview_url = preview
    else:
        # For non-data URLs, return a link to the file
        preview_url = f"/api/v1/media/{media_id}/file"

    return MediaPreview(
        id=media.id,
        media_type=media.media_type,
        content_type=media.content_type,
        preview_url=preview_url,
        metadata=media.metadatas
    )


@router.get("/", response_model=List[MediaSchema])
def list_media(
    user_id: Optional[str] = None,
    media_type: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    session: Session = Depends(get_session)
):
    """List media with optional filtering."""
    query = select(Media)

    if user_id:
        query = query.where(Media.user_id == user_id)

    if media_type:
        query = query.where(Media.media_type == media_type)

    query = query.order_by(Media.created_at.desc()).offset(offset).limit(limit)
    media = session.exec(query).all()

    return media


@router.delete("/{media_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_media(media_id: str, session: Session = Depends(get_session)):
    """Delete a media."""
    media = session.get(Media, media_id)
    if not media:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Media {media_id} not found"
        )

    # Delete file if it exists
    if media.file_path and os.path.isfile(media.file_path):
        try:
            os.remove(media.file_path)
        except Exception:
            pass

    # Delete metadata file if it exists
    metadata_path = os.path.splitext(media.file_path)[0] + ".json"
    if os.path.isfile(metadata_path):
        try:
            os.remove(metadata_path)
        except Exception:
            pass

    # Delete media record
    session.delete(media)
    session.commit()

    return None
