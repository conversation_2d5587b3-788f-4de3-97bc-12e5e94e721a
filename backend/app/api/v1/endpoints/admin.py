from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPBasicCredentials
import secrets

from ....config import settings

router = APIRouter()
security = HTTPBasic()


def verify_admin(credentials: HTTPBasicCredentials):
    """Verify admin credentials."""
    correct_username = secrets.compare_digest(credentials.username, settings.ADMIN_USERNAME)
    correct_password = secrets.compare_digest(credentials.password, settings.ADMIN_PASSWORD)
    
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    
    return True


@router.get("/config")
def get_config(admin: bool = Depends(verify_admin)):
    """Get the current configuration."""
    config = {
        "PROJECT_NAME": settings.PROJECT_NAME,
        "API_V1_STR": settings.API_V1_STR,
        "DATABASE_PATH": settings.DATABASE_PATH,
        "MEDIA_STORAGE_PATH": settings.MEDIA_STORAGE_PATH,
        "OPENAI_API_KEY": settings.OPENAI_API_KEY and "********",  # Mask API key
        "OPENAI_BASE_URL": settings.OPENAI_BASE_URL,
        "COMFYUI_BASE_URL": settings.COMFYUI_BASE_URL,
    }
    
    return config


@router.post("/config")
def update_config(config: Dict[str, Any], admin: bool = Depends(verify_admin)):
    """Update the configuration."""
    # Update settings
    for key, value in config.items():
        if hasattr(settings, key) and key != "SECRET_KEY":
            setattr(settings, key, value)
    
    # Save settings to file
    settings.save_to_file()
    
    return {"message": "Configuration updated successfully"}


@router.post("/restart")
def restart_server(admin: bool = Depends(verify_admin)):
    """Restart the server (not implemented)."""
    # In a real implementation, this would restart the server
    # For now, we'll just return a message
    return {"message": "Server restart not implemented in this version"}


@router.get("/converters")
def list_converters(admin: bool = Depends(verify_admin)):
    """List all registered converters."""
    from ....core.converters.base import ConverterRegistry
    
    converters = []
    for converter in ConverterRegistry.get_all():
        converters.append({
            "name": converter.name,
            "description": converter.description,
            "input_types": [t.__name__ for t in converter.input_types],
            "output_types": [t.__name__ for t in converter.output_types],
        })
    
    return converters
