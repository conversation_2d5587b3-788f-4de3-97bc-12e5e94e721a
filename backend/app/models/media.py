from typing import Optional, List, Dict, Any, TYPE_CHECKING

from sqlalchemy import Column
from sqlmodel import Field, SQLModel, Relationship, JSON
import uuid

from .base import TimestampMixin, UUIDMixin

if TYPE_CHECKING:
    from .user import User
    from .task import Task



class TaskInputMedia(SQLModel, table=True):
    """Link model for task input media."""
    task_id: str = Field(foreign_key="task.id", primary_key=True)
    media_id: str = Field(foreign_key="media.id", primary_key=True)


class TaskOutputMedia(SQLModel, table=True):
    """Link model for task output media."""
    task_id: str = Field(foreign_key="task.id", primary_key=True)
    media_id: str = Field(foreign_key="media.id", primary_key=True)


class Media(SQLModel, UUIDMixin, TimestampMixin, table=True):
    """Media model for storing media metadata."""
    user_id: Optional[str] = Field(default=None, foreign_key="user.id", index=True)
    media_type: str = Field(index=True)  # text, image, audio, video, model3d
    file_path: Optional[str] = Field(default=None)
    content_type: str = Field(default="")  # MIME type or format
    metadata: Dict[str, Any] = Field(default={}, sa_column=Column(JSON))

    # Relationships
    user: Optional["User"] = Relationship()
    input_tasks: List["Task"] = Relationship(
        back_populates="input_media",
        link_model=TaskInputMedia
    )
    output_tasks: List["Task"] = Relationship(
        back_populates="output_media",
        link_model=TaskOutputMedia
    )

    def __init__(self, **data):
        super().__init__(**data)
        if not self.id:
            self.id = str(uuid.uuid4())
