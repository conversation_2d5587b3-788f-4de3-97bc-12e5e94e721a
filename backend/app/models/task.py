from typing import Optional, List, Dict, Any, TYPE_CHECKING

from sqlalchemy import Column
from sqlmodel import Field, SQLModel, Relationship, JSON
import uuid

from .base import TimestampMixin, UUIDMixin

from .user import User
from .media import Media, TaskInputMedia, TaskOutputMedia


class Task(SQLModel, UUIDMixin, TimestampMixin, table=True):
    """Task model for media conversion tasks."""
    user_id: Optional[str] = Field(default=None, foreign_key="user.id", index=True)
    status: str = Field(default="pending")  # pending, processing, completed, failed
    task_type: str = Field(index=True)  # e.g., text_to_image, audio_to_text
    progress: float = Field(default=0.0)
    error_message: Optional[str] = Field(default=None)
    parameters: Dict[str, Any] = Field(default={}, sa_column=Column(JSON))

    # Relationships
    user: Optional["User"] = Relationship(back_populates="tasks")
    input_media: List["Media"] = Relationship(
        back_populates="input_tasks",
        link_model=TaskInputMedia
    )
    output_media: List["Media"] = Relationship(
        back_populates="output_tasks",
        link_model=TaskOutputMedia
    )

    class Config:
        arbitrary_types_allowed = True

    def __init__(self, **data):
        super().__init__(**data)
        if not self.id:
            self.id = str(uuid.uuid4())
