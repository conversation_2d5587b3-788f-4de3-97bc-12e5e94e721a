background: '#FFFFF'
configurate_methods:
- predefined-model
description:
  en_US: Models provided by Upstage, such as Solar-1-mini-chat.
  zh_Hans: Upstage 提供的模型，例如 Solar-1-mini-chat.
extra:
  python:
    model_sources:
    - models/llm/llm.py
    - models/text_embedding/text_embedding.py
    provider_source: provider/upstage.py
help:
  title:
    en_US: Get your API Key from Upstage
    zh_Hans: 从 Upstage 获取 API Key
  url:
    en_US: https://console.upstage.ai/api-keys
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
label:
  en_US: Upstage
model_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Key
    placeholder:
      en_US: Enter your API Key
      zh_Hans: 在此输入您的 API Key
    required: true
    type: secret-input
    variable: upstage_api_key
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
models:
  llm:
    position: models/llm/_position.yaml
    predefined:
    - models/llm/*.yaml
  text_embedding:
    position: models/text_embedding/_position.yaml
    predefined:
    - models/text_embedding/*.yaml
provider: upstage
provider_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Key
    placeholder:
      en_US: Enter your API Key
      zh_Hans: 在此输入您的 API Key
    required: true
    type: secret-input
    variable: upstage_api_key
supported_model_types:
- llm
- text-embedding
