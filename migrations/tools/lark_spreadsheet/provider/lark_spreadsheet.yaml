credentials_for_provider:
  app_id:
    help:
      en_US: Get your app_id and app_secret from Lark
      zh_Hans: 从 Lark 获取您的 app_id 和 app_secret
    label:
      en_US: APP ID
    placeholder:
      en_US: Please input your Lark app id
      zh_Hans: 请输入你的 Lark app id
    required: true
    type: text-input
    url: https://open.larksuite.com/app
  app_secret:
    label:
      en_US: APP Secret
    placeholder:
      en_US: Please input your app secret
      zh_Hans: 请输入你的 Lark app secret
    required: true
    type: secret-input
extra:
  python:
    source: provider/lark_spreadsheet.py
identity:
  author: <PERSON> Lea
  description:
    en_US: 'Lark Spreadsheet, requires the following permissions: sheets:spreadsheet.

      '
    zh_Hans: 'Lark 电子表格，需要开通以下权限: sheets:spreadsheet。

      '
  icon: icon.png
  label:
    en_US: Lark Spreadsheet
    zh_Hans: Lark 电子表格
  name: lark_spreadsheet
  tags:
  - social
  - productivity
tools:
- tools/create_spreadsheet.yaml
- tools/add_rows.yaml
- tools/read_rows.yaml
- tools/read_table.yaml
- tools/get_spreadsheet.yaml
- tools/read_cols.yaml
- tools/add_cols.yaml
- tools/list_spreadsheet_sheets.yaml
