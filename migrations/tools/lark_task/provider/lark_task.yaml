credentials_for_provider:
  app_id:
    help:
      en_US: Get your app_id and app_secret from Lark
      zh_Hans: 从 Lark 获取您的 app_id 和 app_secret
    label:
      en_US: APP ID
    placeholder:
      en_US: Please input your Lark app id
      zh_Hans: 请输入你的 Lark app id
    required: true
    type: text-input
    url: https://open.larksuite.com/app
  app_secret:
    label:
      en_US: APP Secret
    placeholder:
      en_US: Please input your app secret
      zh_Hans: 请输入你的 Lark app secret
    required: true
    type: secret-input
extra:
  python:
    source: provider/lark_task.py
identity:
  author: <PERSON>
  description:
    en_US: 'Lark Task, requires the following permissions: task:task:write、contact:user.id:readonly.

      '
    zh_Hans: 'Lark 任务，需要开通以下权限: task:task:write、contact:user.id:readonly。

      '
  icon: icon.png
  label:
    en_US: Lark Task
    zh_Hans: Lark 任务
  name: lark_task
  tags:
  - social
  - productivity
tools:
- tools/delete_task.yaml
- tools/create_task.yaml
- tools/update_task.yaml
- tools/add_members.yaml
